import request from '@/plugins/axios'
//续费订单列表
export const apiRenewLists = (params: any) => request.get('/settings.shop.renew/lists', { params })

//获取商户当前套餐
export const apiRenewSetMeal = (params?: any) => request.get('/settings.shop.renew/setMeal', { params })

//获取当前订单详情
export const apiRenewDetail = (params?: any) => request.get('/settings.shop.renew/detail', { params })

// 续费下单
export const apiRenewPlaceOrder = (params: any) => request.post('/settings.shop.renew/placeOrder', params)

// 续费页面信息
export const apiRenewInfo = (params?: any) => request.get('/settings.shop.renew/index', { params })

//获取支付方式
export const apiPayWays = () => request.get('/settings.shop.renew/payWays')

// 确认支付
export const apiRenewPay = (params: any) => request.post('/settings.shop.renew/confirmPay', params)

// 支付状态
export const apiRenewPayStatus = (params: any) => request.get('/settings.shop.renew/payStatus', { params })

// 取消支付
export const apiRenewCancel = (params: any) => request.post('/settings.shop.renew/cancel', params)
