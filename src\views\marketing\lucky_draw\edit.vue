<template>
    <div class="lucky-draw-edit">
        <!-- 导航头部 -->
        <div class="ls-card">
            <el-page-header v-if="type == 'details'" @back="$router.go(-1)" content="抽奖活动详情" />
            <el-page-header v-else @back="$router.go(-1)" :content="mode === 'add' ? '新增抽奖活动' : '编辑抽奖活动'" />
        </div>

        <!-- 主要内容 -->
        <el-form
            :rules="formRules"
            ref="formRef"
            :model="form"
            label-width="120px"
            size="small"
            :disabled="type == 'details'"
        >
            <div class="ls-card m-t-16">
                <div class="card-title">基本设置</div>
                <div class="card-content m-t-24">
                    <el-form-item label="活动名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入活动名称"></el-input>
                    </el-form-item>
                    <el-form-item label="活动时间" required>
                        <date-picker
                            type="datetimerange"
                            :start-time.sync="form.start_time"
                            :end-time.sync="form.end_time"
                        />
                    </el-form-item>
                    <el-form-item label="活动备注" prop="remark">
                        <el-input
                            class="ls-input-textarea"
                            v-model="form.remark"
                            placeholder="请输入活动备注"
                            type="textarea"
                            :rows="3"
                            :disabled="status == 1"
                        >
                        </el-input>
                    </el-form-item>
                </div>
            </div>

            <div class="ls-card m-t-16">
                <div class="card-title">活动设置</div>
                <div class="card-content m-t-24">
                    <el-form-item label="消耗积分" prop="need_integral">
                        <el-input
                            v-model="form.need_integral"
                            placeholder="请输入消耗积分"
                            :disabled="status == 1"
                        ></el-input>
                        <div class="muted xs">每次抽奖消耗的积分数量</div>
                    </el-form-item>

                    <el-form-item label="抽奖次数" prop="frequency_type">
                        <!-- <el-radio-group class="m-r-16" v-model="form.frequency_type" :disabled="status == 1"> -->
                        <div class="">
                            <el-radio class="m-r-16" v-model="form.frequency_type" :label="0" :disabled="status == 1"
                                >不限制抽奖次数</el-radio
                            >
                        </div>
                        <div class="">
                            <el-radio v-model="form.frequency_type" :label="1" :disabled="status == 1">
                                <span class="m-r-5">每人每天抽奖不超过</span>
                                <el-input
                                    class="ls-input"
                                    placeholder="请输入抽奖次数"
                                    v-model="form.frequency"
                                    :disabled="status == 1"
                                >
                                </el-input>
                                <span class="m-l-5">次</span>
                            </el-radio>
                        </div>
                        <!-- </el-radio-group> -->
                    </el-form-item>

                    <el-form-item label="抽奖奖品" prop="prizes" required>
                        <div class="muted xs">
                            需要设置8个奖品。抽中概率总和不能超过100%，未中奖类型的抽中概率无需填写
                        </div>
                        <!-- 列表 -->
                        <div class="list-table m-t-16">
                            <el-table
                                :data="form.prizes"
                                style="width: 100%"
                                size="mini"
                                v-loading="false"
                                :header-cell-style="{ background: '#f5f8ff' }"
                            >
                                <el-table-column prop="" label="位置">
                                    <template slot-scope="scope">
                                        <div class="">
                                            {{ scope.$index + 1 }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="name" label="奖品名称"> </el-table-column>
                                <el-table-column label="奖品图片">
                                    <template slot-scope="scope">
                                        <div class="flex" v-if="scope.row.image">
                                            <el-image :src="scope.row.image" style="width: 34px; height: 34px">
                                            </el-image>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="type_desc" label="奖品类型"> </el-table-column>
                                <el-table-column prop="num" label="奖品数量"> </el-table-column>
                                <el-table-column prop="probability" label="中奖概率" v-if="mode == 'add'">
                                </el-table-column>
                                <el-table-column prop="probability_desc" label="中奖概率" v-if="mode == 'edit'">
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" min-width="100">
                                    <template slot-scope="scope">
                                        <ls-lucky-draw-change
                                            title="编辑奖品"
                                            :val="scope.row"
                                            :index="scope.$index"
                                            @setPrize="setPrize"
                                            :status="status"
                                            :mode="mode"
                                        >
                                            <el-button type="text" size="small" slot="trigger" :disabled="status == 1">
                                                编辑</el-button
                                            >
                                        </ls-lucky-draw-change>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-form-item>

                    <el-form-item label="抽奖规则" prop="rule">
                        <el-input
                            class="ls-input-textarea"
                            v-model="form.rule"
                            placeholder="请输入抽奖规则"
                            type="textarea"
                            :rows="3"
                            :disabled="status == 1"
                        >
                        </el-input>
                    </el-form-item>

                    <el-form-item label="中奖名单" prop="show_winning_list">
                        <div class="flex">
                            <el-switch
                                v-model="form.show_winning_list"
                                :active-value="1"
                                :inactive-value="0"
                                :active-color="styleConfig.primary"
                                inactive-color="#f4f4f5"
                                :disabled="status == 1"
                            />
                            <span class="m-l-16">{{ form.show_winning_list ? '显示' : '隐藏' }}</span>
                        </div>
                    </el-form-item>
                </div>
            </div>
        </el-form>

        <!-- 底部保存或取消 -->
        <div class="bg-white ls-fixed-footer">
            <div class="row-center flex" style="height: 100%">
                <el-button size="small" @click="$router.go(-1)">取消</el-button>
                <el-button size="small" type="primary" @click="onSubmit()" :disabled="type == 'details'"
                    >保存</el-button
                >
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
    apiLuckyDrawEdit,
    apiLuckyDrawDetail,
    apiLuckyDrawAdd,
    apiLuckyDrawGetPrizeType
} from '@/api/marketing/lucky_draw'
import { PageMode } from '@/utils/type'
import LsPagination from '@/components/ls-pagination.vue'
import DatePicker from '@/components/date-picker.vue'
import LsLuckyDrawChange from '@/components/lucky-draw/ls-lucky-draw-change.vue'
import { deepClone } from '@/utils/util.ts'
@Component({
    components: {
        LsPagination,
        DatePicker,
        LsLuckyDrawChange
    }
})
export default class LuckyDrawEdit extends Vue {
    mode: string = PageMode.ADD // 当前页面【add: 添加 | edit: 编辑】
    identity: number | null = null // 当前编辑的ID  valid: mode = 'edit'

    status: number | null = null // 当前编辑的状态  valid: status = 0-未开始 1-进行中
    type = ''

    prizeType = 0 //  0-未中奖; 1-积分; 2-优惠券; 3-余额;

    form = {
        name: '', // 活动名称
        start_time: '', // 开始时间，时间戳
        end_time: '', // 结束时间，时间戳
        need_integral: 0, // 需要消耗的积分
        frequency_type: 0, // 抽奖次数类型
        frequency: 0, // 抽奖次数
        rule: '', // 抽奖规则
        show_winning_list: 0, // 是否显示中奖名单
        remark: '', // 备注
        prizes: [{}] // 奖品
        // prizes.name	: '', // 奖品名称
        // prizes.image: '', // 奖品图片
        // prizes.type: '', // 奖品类型
        // prizes.type_value: '', // 奖品类型值
        // prizes.num: '', // 奖品数量
        // prizes.probability: '', // 中奖概率
        // prizes.tips: '', // 中奖提示
    }

    $refs!: {
        formRef: any
    }
    formRules = {
        name: [
            {
                required: true,
                message: '请输入活动名称',
                trigger: 'blur'
            }
        ],
        start_time: [
            {
                required: true,
                message: '请选择活动时间',
                trigger: 'change'
            }
        ],
        end_time: [
            {
                required: true,
                message: '请选择活动时间',
                trigger: 'change'
            }
        ],
        need_integral: [
            {
                required: true,
                message: '请输入消耗积分',
                trigger: 'blur'
            }
        ],
        frequency_type: [
            {
                required: true,
                message: '请选择抽奖次数',
                trigger: 'change'
            }
        ],
        rule: [
            {
                required: true,
                message: '请输入抽奖规则',
                trigger: 'blur'
            }
        ],
        show_winning_list: [
            {
                required: true,
                message: '请选择中奖名单是否隐藏',
                trigger: 'blur'
            }
        ]
    }
    lists = [
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        },
        {
            name: '', // 奖品名称
            image: '', // 奖品图片
            type: 0, // 奖品类型 // 0-未中奖; 1-积分; 2-优惠券; 3-余额;
            type_value: 0, // 奖品值
            type_desc: '', // 奖品描述
            num: 0, // 奖品数量
            probability: 0, // 中奖概率
            tips: '', // 中奖提示
            status: '',
            probability_desc: 0
        }
    ]

    setPrize(obj: any, index: any) {
        // this.form.prizes[index] = deepClone(obj)
        this.$set(this.form.prizes, index, deepClone(obj))
        this.$forceUpdate()
    }

    checkPrizes() {
        let isPass = true
        // 验证礼品

        for (let i = 0; i < this.form.prizes.length; i++) {
            const type = (this.form.prizes[i] as any).type

            if ((this.form.prizes[i] as any).name == '') {
                this.$message.error(`请输入位置${i + 1}的奖品名称`)
                return (isPass = false)
            }
            if ((this.form.prizes[i] as any).image == '') {
                this.$message.error(`请选择位置${i + 1}的奖品图片`)
                return (isPass = false)
            }
            if ((this.form.prizes[i] as any).tips == '') {
                this.$message.error(`请输入位置${i + 1}的抽中提示语`)
                return (isPass = false)
            }
            if (type != 0 && !(this.form.prizes[i] as any).num) {
                this.$message.error(`请输入位置${i + 1}的奖品数量`)
                return (isPass = false)
            }
            if (type != 0 && !(this.form.prizes[i] as any).probability) {
                this.$message.error(`请输入位置${i + 1}的中奖概率`)
                return (isPass = false)
            }
            if (type != 0 && (this.form.prizes[i] as any).type_value == '') {
                this.$message.error(
                    `请输入位置${i + 1}的${type == 1 ? '积分' : type == 2 ? '优惠券' : type == 3 ? '余额' : ''}`
                )
                return (isPass = false)
            }
        }

        return isPass
    }

    onSubmit() {
        this.$refs.formRef.validate((valid: boolean): any => {
            if (!valid) {
                return
            }

            this.$nextTick(() => {
                if (!this.checkPrizes()) {
                    return
                }

                // 提交请求
                if (this.mode == PageMode.ADD) {
                    this.luckyDrawAdd()
                } else if (this.mode == PageMode.EDIT) {
                    this.luckyDrawEdit()
                }
            })
        })
    }

    luckyDrawDetail() {
        apiLuckyDrawDetail({
            id: this.identity
        }).then((res: any) => {
            res.start_time = res.start_time_desc
            res.end_time = res.end_time_desc

            this.form = res
        })
    }

    luckyDrawEdit() {
        apiLuckyDrawEdit(this.form)
            .then((res: any) => {
                setTimeout(() => {
                    this.$router.go(-1)
                }, 500)
            })
            .catch((err: any) => {})
    }

    luckyDrawAdd() {
        apiLuckyDrawAdd(this.form)
            .then((res: any) => {
                setTimeout(() => {
                    this.$router.go(-1)
                }, 500)
            })
            .catch((err: any) => {})
    }

    created() {
        const query: any = this.$route.query

        if (query.mode) {
            this.mode = query.mode
        }

        // 编辑模式：初始化数据
        if (this.mode === PageMode.EDIT) {
            this.identity = query.id * 1
            this.status = query.status
            this.type = query.type
            this.luckyDrawDetail()
        } else {
            this.form.prizes = this.lists
        }
    }
}
</script>

<style lang="scss" scoped>
.ls-card {
    .ls-input {
        width: 180px;
    }

    .ls-input-textarea {
        width: 300px;
    }

    .card-title {
        font-size: 14px;
        font-weight: 500;
    }
}

.lucky-draw-edit {
    min-height: calc(100vh - #{$--header-height} - 92px);
    margin-bottom: 60px;
}
</style>
