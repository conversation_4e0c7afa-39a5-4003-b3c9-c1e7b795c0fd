<template>
    <div class="distribution-user-detail" v-loading="loading">
        <!-- 导航头部 -->
        <div class="header-container">
            <el-page-header @back="goBack" content="分销用户详情" />
        </div>

        <!-- 用户基本信息 -->
        <div class="user-info-container">
            <el-card>
                <div slot="header" class="card-header">
                    <span>用户信息</span>
                </div>
                <div class="user-info" v-if="userDetail.user_id">
                    <div class="info-item">
                        <label>用户ID：</label>
                        <span>{{ userDetail.user_id }}</span>
                    </div>
                    <div class="info-item">
                        <label>昵称：</label>
                        <span>{{ userDetail.nickname }}</span>
                    </div>
                    <div class="info-item">
                        <label>手机号：</label>
                        <span>{{ userDetail.mobile }}</span>
                    </div>
                    <div class="info-item">
                        <label>等级：</label>
                        <span>{{ userDetail.level }}</span>
                    </div>
                    <div class="info-item">
                        <label>邀请人数：</label>
                        <span>{{ userDetail.invite_count }}</span>
                    </div>
                    <div class="info-item">
                        <label>总佣金：</label>
                        <span>¥{{ userDetail.total_commission }}</span>
                    </div>
                    <div class="info-item">
                        <label>可用余额：</label>
                        <span>¥{{ userDetail.available_amount }}</span>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 统计信息 -->
        <div class="stats-container">
            <el-row :gutter="20">
                <!-- 佣金统计 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header" class="card-header">
                            <span>佣金统计</span>
                        </div>
                        <div class="stats-content" v-if="userDetail.commission_stats">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.commission_stats.total_commission }}</div>
                                <div class="stat-label">总佣金</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.commission_stats.settled_commission }}</div>
                                <div class="stat-label">已结算</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.commission_stats.pending_commission }}</div>
                                <div class="stat-label">待结算</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>

                <!-- 提现统计 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header" class="card-header">
                            <span>提现统计</span>
                        </div>
                        <div class="stats-content" v-if="userDetail.withdraw_stats">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.withdraw_stats.total_withdraw }}</div>
                                <div class="stat-label">总提现</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.withdraw_stats.success_withdraw }}</div>
                                <div class="stat-label">成功提现</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥{{ userDetail.withdraw_stats.pending_withdraw }}</div>
                                <div class="stat-label">待审核</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 下级用户 -->
        <div class="children-container">
            <el-row :gutter="20">
                <!-- 一级下级 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header" class="card-header">
                            <span>一级下级 ({{ userDetail.first_children ? userDetail.first_children.length : 0 }})</span>
                        </div>
                        <div class="children-list">
                            <div
                                v-for="child in userDetail.first_children"
                                :key="child.user_id"
                                class="child-item"
                            >
                                <div class="child-info">
                                    <div class="child-name">{{ child.nickname }}</div>
                                    <div class="child-mobile">{{ child.mobile }}</div>
                                </div>
                                <div class="child-stats">
                                    <div class="child-commission">¥{{ child.total_commission }}</div>
                                </div>
                            </div>
                            <el-empty v-if="!userDetail.first_children || userDetail.first_children.length === 0" 
                                     description="暂无一级下级" />
                        </div>
                    </el-card>
                </el-col>

                <!-- 二级下级 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header" class="card-header">
                            <span>二级下级 ({{ userDetail.second_children ? userDetail.second_children.length : 0 }})</span>
                        </div>
                        <div class="children-list">
                            <div 
                                v-for="child in userDetail.second_children" 
                                :key="child.user_id"
                                class="child-item"
                            >
                                <div class="child-info">
                                    <div class="child-name">{{ child.nickname }}</div>
                                    <div class="child-mobile">{{ child.mobile }}</div>
                                </div>
                                <div class="child-stats">
                                    <div class="child-commission">¥{{ child.total_commission }}</div>
                                </div>
                            </div>
                            <el-empty v-if="!userDetail.second_children || userDetail.second_children.length === 0" 
                                     description="暂无二级下级" />
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiDistributionPromotionUserDetail } from '@/api/distribution_promotion/distribution_promotion'
import * as Interface from '@/api/distribution_promotion/distribution_promotion.d'

@Component({
    name: 'DistributionUserDetail'
})
export default class DistributionUserDetail extends Vue {
    // 用户详情
    private userDetail: Interface.DistributionUserDetailRes = {} as Interface.DistributionUserDetailRes

    // 加载状态
    private loading = false

    // 用户ID
    private userId = ''

    created() {
        const userId = this.$route.query.user_id
        if (userId) {
            this.userId = userId as string
            this.getUserDetail()
        } else {
            this.$message.error('用户ID不能为空')
            this.goBack()
        }
    }

    // 获取用户详情
    async getUserDetail() {
        try {
            this.loading = true
            const res = await apiDistributionPromotionUserDetail({ user_id: parseInt(this.userId) })
            if (res) {
                this.userDetail = res
            }
        } catch (error) {
            console.error('获取用户详情失败', error)
            this.$message.error('获取用户详情失败')
        } finally {
            this.loading = false
        }
    }

    // 返回列表
    goBack() {
        this.$router.push('/distribution_promotion/user_list')
    }
}
</script>

<style lang="scss" scoped>
.distribution-user-detail {
    padding: 20px;

    .header-container {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }

    .user-info-container {
        margin-bottom: 20px;

        .user-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;

            .info-item {
                display: flex;
                align-items: center;

                label {
                    font-weight: bold;
                    margin-right: 10px;
                    min-width: 80px;
                }
            }
        }
    }

    .stats-container {
        margin-bottom: 20px;

        .stats-content {
            display: flex;
            justify-content: space-around;

            .stat-item {
                text-align: center;

                .stat-value {
                    font-size: 24px;
                    font-weight: bold;
                    color: #409eff;
                    margin-bottom: 5px;
                }

                .stat-label {
                    color: #666;
                    font-size: 14px;
                }
            }
        }
    }

    .children-container {
        .children-list {
            max-height: 400px;
            overflow-y: auto;

            .child-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .child-info {
                    .child-name {
                        font-weight: bold;
                        margin-bottom: 5px;
                    }

                    .child-mobile {
                        color: #666;
                        font-size: 12px;
                    }
                }

                .child-stats {
                    .child-commission {
                        color: #409eff;
                        font-weight: bold;
                    }
                }
            }
        }
    }

    .card-header {
        font-weight: bold;
    }
}

@media (max-width: 768px) {
    .distribution-user-detail {
        .user-info {
            grid-template-columns: 1fr !important;
        }
    }
}
</style>
