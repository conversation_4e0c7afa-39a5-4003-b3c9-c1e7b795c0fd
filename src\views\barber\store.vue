<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiSubShopList } from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'

@Component({
    name: '店铺列表',
    components: {
        LsPagination
    }
})
export default class BarberStore extends Vue {
    // 搜索表单
    private searchForm = {
        name: ''
    }

    // 分页数据
    private pager = new RequestPaging()

    created() {
        this.getList()
    }

    // 获取子店铺列表
    async getList() {
        try {
            await this.pager.request({
                callback: apiSubShopList,
                params: this.searchForm
            })
        } catch (error) {
            console.error('获取子店铺列表失败', error)
        }
    }

    // 搜索
    handleSearch() {
        this.pager.page = 1
        this.getList()
    }

    // 重置搜索
    resetSearch() {
        this.searchForm.name = ''
        this.handleSearch()
    }

    // 新增店铺
    handleAddStore() {
        this.$router.push('/store/store_detail')
    }

    // 编辑店铺
    handleEditStore(row: any) {
        // 只传递ID参数到详情页面
        this.$router.push({
            path: '/store/store_detail',
            query: { id: row.id }
        })
    }

}
</script>

<template>
    <div class="store-container">
        <!-- 页面标题 -->
        <div class="page-title">店铺管理</div>

        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="店铺名称">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入店铺名称"
                        clearable
                        @keyup.enter.native="handleSearch"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <div class="tool-container">
            <el-button type="primary" @click="handleAddStore">新增店铺</el-button>
        </div>

        <!-- 表格 -->
        <div class="table-container">
            <el-table
                :data="pager.lists"
                v-loading="pager.loading"
                border
                style="width: 100%"
                size="mini"
            >
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="name" label="店铺名称"></el-table-column>
                <el-table-column prop="detailed_address" label="店铺地址"></el-table-column>
                <el-table-column prop="contact" label="联系人"></el-table-column>
                <el-table-column prop="mobile" label="联系电话"></el-table-column>
                <el-table-column label="店铺状态" width="100">
                    <template v-slot="slotProps">
                        <el-tag :type="slotProps.row.status === 1 ? 'success' : 'info'">
                            {{ slotProps.row.status === 1 ? '正常营业' : '暂停营业' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template v-slot="slotProps">
                        <el-button
                            type="text"
                            size="small"
                            @click="handleEditStore(slotProps.row)"
                        >编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <ls-pagination v-model="pager" @change="getList()" />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.store-container {
    padding: 20px;

    .page-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .search-container, .tool-container {
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .table-container {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>


