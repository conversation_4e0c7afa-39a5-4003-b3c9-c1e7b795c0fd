<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { apiMemberList, apiUpdateMemberPhone, apiMemberOpenCard } from '@/api/member/member'
import { apiBarberList } from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'
import config from '@/config'

@Component({
  name: 'MemberList',
  components: {
    LsPagination
  }
})
export default class MemberList extends Vue {
  // 搜索表单
  private searchForm = {
    mobile: '',
    nickname: ''
  }

  // 分页数据
  private pager = new RequestPaging()

  // 加载状态
  private loading = false

  // baseURL
  private baseURL = config.baseURL

  // 理发师列表
  private barberList: any[] = []

  // 修改会员信息弹窗
  private editDialog = {
    visible: false,
    title: '修改会员信息'
  }

  // 修改会员表单
  private editForm = {
    id: '',
    mobile: '',
    type: 1, // 1增加积分，2减少积分
    points: 0
  }

  // 修改会员表单规则
  private editRules = {
    mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    points: [{ required: true, message: '请输入积分', trigger: 'blur' }]
  }

  // 开卡弹窗
  private cardDialog = {
    visible: false,
    title: '会员开卡'
  }

  // 开卡表单
  private cardForm = {
    id: '',
    type: 1, // 1会员卡，2次卡
    salesperson_id: '', // 销售人员ID（理发师ID）
    mobile: '', // 手机号
    price: '',
    title: '',
    num: '',
    totl_price: '', // 总金额（次卡专用）
    single_price: '' // 单次金额（次卡专用）
  }

  // 开卡表单规则
  private get cardRules() {
    return {
      type: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
      salesperson_id: [{ required: true, message: '请选择销售人员', trigger: 'change' }],
      mobile: [
        {
          required: !this.isPhoneDisabled,
          message: '请输入手机号',
          trigger: 'blur'
        },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号格式',
          trigger: 'blur'
        }
      ],
      price: [{ required: true, message: '请输入金额', trigger: 'blur' }],
      title: [{ required: true, message: '请输入卡名称', trigger: 'blur' }],
      num: [{ required: true, message: '请输入次数', trigger: 'blur' }],
      totl_price: [{ required: true, message: '请输入总金额', trigger: 'blur' }],
      single_price: [{ required: true, message: '请输入单次金额', trigger: 'blur' }]
    }
  }

  // 计算属性：判断手机号输入框是否应该禁用
  get isPhoneDisabled() {
    // 检查表单中的会员ID是否有效
    if (!this.cardForm.id) {
      console.log('cardForm.id 为空，允许输入手机号')
      return false
    }

    // 查找当前会员
    const currentMember = this.pager.lists.find((item: any) => item.id === this.cardForm.id)
    if (!currentMember) {
      console.log('未找到当前会员，允许输入手机号')
      return false
    }

    // 检查手机号是否存在且不为空
    const mobile = currentMember.mobile
    const isValidMobile = Boolean(mobile && typeof mobile === 'string' && mobile.trim().length > 0)

    console.log('会员手机号检查:', {
      memberId: this.cardForm.id,
      mobile: mobile,
      isValidMobile: isValidMobile,
      type: typeof mobile,
      trimmedLength: mobile ? mobile.trim().length : 0
    })

    return isValidMobile
  }

  // 监听手机号禁用状态变化
  @Watch('isPhoneDisabled')
  onPhoneDisabledChange(newVal: boolean) {
    if (newVal && this.cardForm.mobile) {
      // 如果手机号被禁用且有值，清除该字段的验证错误
      this.$nextTick(() => {
        const formRef = this.$refs.cardForm as any
        if (formRef) {
          formRef.clearValidate('mobile')
        }
      })
    }
  }

  created() {
    this.getList()
    this.getBarberList()
  }

  // 获取会员列表
  async getList() {
    try {
      this.loading = true
      await this.pager.request({
        callback: apiMemberList,
        params: this.searchForm
      })
    } catch (error) {
      console.error('获取会员列表失败', error)
    } finally {
      this.loading = false
    }
  }

  // 获取理发师列表
  async getBarberList() {
    try {
      const res = await apiBarberList({
        page_no: 1,
        page_size: 10000
      })
      this.barberList = res.lists || []
    } catch (error) {
      console.error('获取理发师列表失败', error)
    }
  }

  // 搜索
  handleSearch() {
    this.pager.page = 1
    this.getList()
  }

  // 重置搜索
  resetSearch() {
    this.searchForm.mobile = ''
    this.searchForm.nickname = ''
    this.handleSearch()
  }

  // 查看会员卡
  handleViewCards(row: any) {
    this.$router.push(`/member/list_detail?id=${row.id}`)
  }

  // 打开修改会员信息弹窗
  handleOpenEditDialog(row: any) {
    this.editForm = {
      id: row.id,
      mobile: row.mobile,
      type: 1,
      points: 0
    }
    this.editDialog.visible = true
  }

  // 提交修改会员信息表单
  async submitEditForm() {
    const formRef = this.$refs.editForm as any
    if (!formRef) {
return
}

    try {
      await formRef.validate()

      this.loading = true
      try {
        await apiUpdateMemberPhone(this.editForm)
        this.$message.success('修改成功')
        this.editDialog.visible = false
        this.getList()
      } catch (error) {
        console.error('修改失败', error)
        this.$message.error('修改失败')
      } finally {
        this.loading = false
      }
    } catch (error) {
      console.error('表单验证失败', error)
    }
  }

  // 打开开卡弹窗
  handleOpenCardDialog(row: any) {
    // 处理手机号：只有当手机号存在且不为空时才自动填充
    let mobile = ''
    if (row.mobile && typeof row.mobile === 'string') {
      const trimmedMobile = row.mobile.trim()
      if (trimmedMobile !== '') {
        mobile = trimmedMobile
      }
    }

    console.log('打开开卡弹窗:', { rowMobile: row.mobile, processedMobile: mobile })

    this.cardForm = {
      id: row.id,
      type: 1,
      salesperson_id: '',
      mobile: mobile, // 智能填充手机号
      price: '',
      title: '',
      num: '',
      totl_price: '',
      single_price: ''
    }
    this.cardDialog.visible = true

    // 清除之前的表单验证错误
    this.$nextTick(() => {
      const formRef = this.$refs.cardForm as any
      if (formRef) {
        formRef.clearValidate()
      }
    })
  }

  // 提交开卡表单
  async submitCardForm() {
    const formRef = this.$refs.cardForm as any
    if (!formRef) {
return
}

    try {
      await formRef.validate()

      this.loading = true
      try {
        await apiMemberOpenCard(this.cardForm)
        this.$message.success('开卡成功')
        this.cardDialog.visible = false
        this.getList()
      } catch (error) {
        console.error('开卡失败', error)
        this.$message.error('开卡失败')
      } finally {
        this.loading = false
      }
    } catch (error) {
      console.error('表单验证失败', error)
    }
  }

  // 监听卡类型变化
  handleCardTypeChange(value: number) {
    // 重置相关字段（但保留手机号）
    if (value === 1) {
      // 会员卡
      this.cardForm.title = ''
      this.cardForm.num = ''
      this.cardForm.totl_price = ''
      this.cardForm.single_price = ''
      this.cardRules.title[0].required = false
      this.cardRules.num[0].required = false
      this.cardRules.totl_price[0].required = false
      this.cardRules.single_price[0].required = false
      this.cardRules.price[0].required = true
    } else {
      // 次卡
      this.cardForm.price = ''
      this.cardRules.price[0].required = false
      this.cardRules.title[0].required = true
      this.cardRules.num[0].required = true
      this.cardRules.totl_price[0].required = true
      this.cardRules.single_price[0].required = true
    }
  }
}
</script>

<template>
  <div class="member-list-container">
    <!-- 页面标题 -->
    <div class="page-title">会员列表</div>

    <!-- 搜索栏 -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.mobile"
            placeholder="请输入手机号"
            clearable
            @keyup.enter.native="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item label="会员昵称">
          <el-input
            v-model="searchForm.nickname"
            placeholder="请输入会员昵称"
            clearable
            @keyup.enter.native="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="pager.lists"
        v-loading="pager.loading"
        border
        style="width: 100%"
        size="mini"
      >
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column label="会员信息" min-width="200">
          <template slot-scope="{ row }">
            <div class="member-info">
              <el-avatar
                :size="60"
                :src="baseURL + '/' + row.avatar"
                class="member-avatar"
              ></el-avatar>
              <div class="member-detail">
                <div class="member-name">{{ row.nickname }}</div>
                <div class="member-mobile">{{ row.mobile }}</div>
                <div class="member-sn">编号：{{ row.sn }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分"></el-table-column>
        <el-table-column label="会员状态">
          <template slot-scope="{ row }">
            <el-tag :type="row.state === 1 ? 'success' : 'info'">
              {{ row.state === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template slot-scope="{ row }">
            <el-button
              type="primary"
              size="mini"
              @click="handleViewCards(row)"
            >查看会员卡</el-button>
            <el-button
              type="success"
              size="mini"
              @click="handleOpenCardDialog(row)"
            >开卡</el-button>
            <el-button
              type="warning"
              size="mini"
              @click="handleOpenEditDialog(row)"
            >修改积分</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <ls-pagination v-model="pager" @change="getList()" />
      </div>
    </div>

    <!-- 修改会员信息弹窗 -->
    <el-dialog
      :title="editDialog.title"
      :visible.sync="editDialog.visible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="editForm.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>

        <el-form-item label="积分操作" prop="type">
          <el-radio-group v-model="editForm.type">
            <el-radio :label="1">增加积分</el-radio>
            <el-radio :label="2">减少积分</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="积分" prop="points">
          <el-input-number v-model="editForm.points" :min="0" :step="1"></el-input-number>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitEditForm" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 开卡弹窗 -->
    <el-dialog
      :title="cardDialog.title"
      :visible.sync="cardDialog.visible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="cardForm"
        :model="cardForm"
        :rules="cardRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="卡类型" prop="type">
          <el-radio-group v-model="cardForm.type" @change="handleCardTypeChange">
            <el-radio :label="1">会员卡</el-radio>
            <el-radio :label="2">次卡</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="销售人员" prop="salesperson_id">
          <el-select v-model="cardForm.salesperson_id" placeholder="请选择销售人员" style="width: 100%">
            <el-option
              v-for="barber in barberList"
              :key="barber.id"
              :label="barber.name"
              :value="barber.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="cardForm.mobile"
            placeholder="请输入手机号"
            maxlength="11"
            :disabled="isPhoneDisabled"
          >
            <template slot="prepend">
              <i class="el-icon-phone"></i>
            </template>
          </el-input>
          <div v-if="isPhoneDisabled" class="phone-tip">
            <i class="el-icon-info"></i>
            <span>该会员已绑定手机号：{{ cardForm.mobile }}，将使用此手机号</span>
          </div>
          <div v-else class="phone-tip">
            <i class="el-icon-warning"></i>
            <span>请输入会员手机号，开卡时必须绑定手机号</span>
          </div>
        </el-form-item>

        <el-form-item label="金额" prop="price" v-if="cardForm.type === 1">
          <el-input v-model="cardForm.price" placeholder="请输入金额" type="number">
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="卡名称" prop="title" v-if="cardForm.type === 2">
          <el-input v-model="cardForm.title" placeholder="请输入卡名称"></el-input>
        </el-form-item>

        <el-form-item label="次数" prop="num" v-if="cardForm.type === 2">
          <el-input-number v-model="cardForm.num" :min="1" :step="1"></el-input-number>
        </el-form-item>

        <el-form-item label="总金额" prop="totl_price" v-if="cardForm.type === 2">
          <el-input v-model="cardForm.totl_price" placeholder="请输入总金额" type="number">
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="单次金额" prop="single_price" v-if="cardForm.type === 2">
          <el-input v-model="cardForm.single_price" placeholder="请输入单次金额" type="number">
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cardDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitCardForm" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.member-list-container {
  padding: 20px;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .search-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .table-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .member-info {
      display: flex;
      align-items: center;

      .member-avatar {
        margin-right: 15px;
        border: 1px solid #ebeef5;
      }

      .member-detail {
        .member-name {
          font-size: 15px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .member-mobile {
          color: #606266;
          font-size: 13px;
          margin-bottom: 3px;
        }

        .member-sn {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .phone-tip {
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
    }
  }
}
</style>
