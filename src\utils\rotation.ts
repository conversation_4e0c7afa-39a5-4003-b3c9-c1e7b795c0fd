export function rotationCreate(callback: any, interval = 2000) {
    let timer: any
    let isStop = false
    const stop = () => {
        isStop = true
        clearTimeout(timer)
    }
    const start = async () => {
        isStop = false
        await loop()
    }
    const loop = async () => {
        try {
            await callback(stop)
        } catch (err) {
            throw new Error(err)
        }
        if (isStop) {
            return
        }
        return (timer = setTimeout(loop, interval))
    }
    return {
        start,
        stop
    }
}
