<template>
    <el-dialog :visible.sync="show" title="支付方式" width="400px" top="20vh" @close="close">
        <div class="p-l-20 p-r-20">
            <detail ref="detail" :data="data" v-model="payWay" />
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="close"> 取消 </el-button>
            <el-button size="small" type="primary" v-if="PayWayEnum.Transfer != payWay" @click="handleConfirm">
                确认支付
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import LsDialog from '@/components/ls-dialog.vue'
import Detail from './detail.vue'
import { PayWayEnum } from '@/utils/pay'
import { throttle } from '@/utils/util'
@Component({
    components: {
        LsDialog,
        Detail
    }
})
export default class Dialog extends Vue {
    @Prop() value!: boolean
    @Prop({ default: () => ({}) }) data: any
    get show() {
        return this.value
    }
    set show(value) {
        this.$emit('input', value)
    }
    payWay = 0
    PayWayEnum = PayWayEnum
    @Watch('payWay')
    payWayChange(value: number) {
        if (value == this.PayWayEnum.Transfer) {
            this.$emit('confirm', this.payWay)
        }
    }

    close() {
        this.show = false
    }
    handleConfirm() {
        this.close()
        this.$emit('confirm', this.payWay)
    }
    created() {
        this.handleConfirm = throttle(this.handleConfirm, 1000)
    }
}
</script>

<style scoped lang="scss">

</style>
