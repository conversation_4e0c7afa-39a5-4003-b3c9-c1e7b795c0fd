<template>
    <el-form>
        <el-form-item label="支付方式">
            <el-radio-group v-model="payMethod" @change="payMethodChange">
                <el-radio label="online">线上支付</el-radio>
                <el-radio label="offline">对公/线下转账</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item>
            <div class="flex" v-if="payMethod == 'online'">
                <div class="pay-way" v-for="item in payWayLists" :key="item.id" @click="payWay = item.pay_way"
                    :class="{ active: payWay == item.pay_way }">
                    <img :src="item.icon" alt="" />
                    <span class="nr">{{ item.name }}</span>
                    <div class="select-icon" v-if="payWay == item.pay_way">
                        <i class="el-icon-check"></i>
                    </div>
                </div>
            </div>
            <div v-if="payMethod == 'offline'" class="offline inline-flex col-center">
                <div class="flex-1">
                    <div class="nr weight-500">线下转账/银行汇款</div>
                    <div class="m-t-10">扫码联系客服获取公帐账户</div>
                    <div class="danger m-t-14">如确认购买，请先提交订单，并联系我们</div>
                </div>
                <img class="qr-code m-l-24" :src="serviceData.service_qrcode" alt="" />
            </div>
        </el-form-item>
    </el-form>
</template>

<script lang="ts">
import { apiServiceContact } from '@/api/home'
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

@Component
export default class PaySelect extends Vue {
    @Prop({ default: () => ({}) }) data: any
    @Prop({ default: 0 }) value!: number
    payMethod = 'online'
    serviceData: any = {}
    get payWay() {
        return this.value
    }
    set payWay(val) {
        this.$emit('input', val)
    }

    get payWayLists() {
        return this.data[this.payMethod] || []
    }
    payMethodChange() {
        this.payWay = this.payWayLists.at(0)?.pay_way
    }
    @Watch('data', { immediate: true })
    watchData(value: any) {
        this.payMethodChange()
    }
    // 打开联系客服
    async getService() {
        this.serviceData = await apiServiceContact()
    }
    created() {
        this.getService()
    }
}
</script>

<style scoped lang="scss">
.pay-way {
    display: flex;
    align-items: center;
    width: 180px;
    height: 70px;
    border-radius: 6px;
    border: 1px solid $--border-color-base;
    padding: 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &.active {
        border-color: $--color-primary;

        & .select-icon {
            width: 40px;
            height: 40px;
            color: #fff;
            position: absolute;
            right: 0;
            top: 0;

            &::after {
                content: '';
                position: absolute;
                display: block;
                border: 20px solid transparent;
                border-top-color: $--color-primary;
                border-right-color: $--color-primary;
            }

            i {
                right: 3px;
                top: 3px;
                position: absolute;
                font-size: 18px;
                z-index: 1;
            }
        }
    }

    img {
        width: 30px;
        height: 30px;
        margin: 0 10px;
    }
}

.offline {
    border: 1px solid $--border-color-base;
    padding: 15px;
    border-radius: 4px;
    line-height: 1.3;

    .qr-code {
        width: 100px;
        height: 100px;
    }
}
</style>
