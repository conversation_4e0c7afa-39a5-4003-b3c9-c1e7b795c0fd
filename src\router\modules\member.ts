import Main from '@/layout/main.vue'
import Blank from '@/layout/blank.vue'


const routes = [
    {
        path: '/member',
        name: 'member',
        meta: {title: '会员',},
        redirect: '/member/list',
        component: Main,
        children: [
            {
                path: '/member/list',
                name: 'member_list',
                meta: {
                    title: '会员列表',
                    parentPath: '/member',
                    icon: 'icon_order_guanli',
                    permission: ['view'],
                    keepAlive: true
                },
                component: () => import('@/views/member/list.vue')
            },
            {
                path: '/member/list_detail',
                name: 'member_list_detail',
                meta: {
                    hidden: true,
                    title: '会员详情',
                    parentPath: '/member',
                    prevPath: '/member/list'
                },
                component: () => import('@/views/member/list_detail.vue')
            }
        ]
    }
]

export default routes