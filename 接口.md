### 分销用户管理

#### 1. 分销用户列表

- **接口地址**: `GET /distribution_promotion/user_list`
- **接口说明**: 获取分销用户列表
- **请求参数**:

- `level` (int): 等级筛选
- `status` (int): 状态筛选
- `nickname` (string): 昵称搜索
- `mobile` (string): 手机号搜索
- `page` (int): 页码
- `limit` (int): 每页数量

- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "user_id": 1,
                "nickname": "张三",
                "mobile": "138****1234",
                "level": 1,
                "level_name": "青铜分销商",
                "invite_count": 5,
                "total_commission": 120.50,
                "available_amount": 85.30,
                "status_text": "启用",
                "parent_nickname": "李四",
                "create_time": "2025-01-29 10:30:00"
            }
        ],
        "total": 100
    }
}
```

#### 2. 分销用户详情

- **接口地址**: `GET /distribution_promotion/user_detail`
- **接口说明**: 获取分销用户详细信息
- **请求参数**:

- `user_id` (int): 用户ID

- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "user_id": 1,
        "nickname": "张三",
        "mobile": "138****1234",
        "level": 1,
        "invite_count": 5,
        "total_commission": 120.50,
        "available_amount": 85.30,
        "first_children": [],
        "second_children": [],
        "commission_stats": {
            "total_commission": 120.50,
            "settled_commission": 100.50,
            "pending_commission": 20.00
        },
        "withdraw_stats": {
            "total_withdraw": 50.00,
            "success_withdraw": 30.00,
            "pending_withdraw": 20.00
        }
    }
}
```

#### 3. 分销统计概览

- **接口地址**: `GET /distribution_promotion/statistics`
- **接口说明**: 获取分销系统统计概览
- **请求参数**: 无
- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "total": {
            "users": 1000,
            "active_users": 800,
            "invites": 5000,
            "commission": 50000.00,
            "withdraw": 20000.00
        },
        "today": {
            "users": 10,
            "invites": 50,
            "commission": 500.00
        },
        "month": {
            "users": 200,
            "invites": 1000,
            "commission": 10000.00
        },
        "withdraw": {
            "total": 20000.00,
            "pending": 5000.00,
            "success": 15000.00
        }
    }
}
```

#### 4. 等级分布统计

- **接口地址**: `GET /distribution_promotion/level_statistics`
- **接口说明**: 获取各等级用户分布统计
- **请求参数**: 无
- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "level": 1,
            "level_name": "青铜分销商",
            "user_count": 500,
            "min_invite": 0,
            "max_invite": 9,
            "first_rate": 5.00,
            "second_rate": 2.00,
            "invite_reward": 10.00
        }
    ]
}
```

#### 5. 禁用/启用分销用户

- **接口地址**: `POST /distribution_promotion/change_status`
- **接口说明**: 更改分销用户状态
- **请求参数**:

- `user_id` (int): 用户ID
- `status` (int): 状态，0=禁用，1=启用

- **返回数据**:

```json
{
    "code": 1,
    "msg": "操作成功"
}
```

### 提现管理

#### 1. 提现申请列表

- **接口地址**: `GET /distribution_withdraw/list`
- **接口说明**: 获取提现申请列表
- **请求参数**:

- `status` (int): 状态筛选
- `nickname` (string): 用户昵称搜索
- `mobile` (string): 手机号搜索
- `page` (int): 页码
- `limit` (int): 每页数量

- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "nickname": "张三",
                "mobile": "138****1234",
                "amount": 50.00,
                "account_type_name": "微信",
                "status_name": "待审核",
                "create_time": "2025-01-29 10:30:00",
                "audit_time": "",
                "audit_admin_name": ""
            }
        ],
        "total": 50
    }
}
```

#### 2. 审核提现申请

- **接口地址**: `POST /distribution_withdraw/audit`
- **接口说明**: 审核提现申请
- **请求参数**:

- `id` (int): 申请ID
- `status` (int): 审核状态，1=通过，2=拒绝
- `remark` (string): 审核备注

- **返回数据**:

```json
{
    "code": 1,
    "msg": "审核成功"
}
```

#### 3. 提现统计

- **接口地址**: `GET /distribution_withdraw/statistics`
- **接口说明**: 获取提现统计数据
- **请求参数**: 无
- **返回数据**:

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "total": {"amount": 50000.00, "count": 500},
        "pending": {"amount": 10000.00, "count": 100},
        "success": {"amount": 35000.00, "count": 350},
        "reject": {"amount": 5000.00, "count": 50},
        "today": {"amount": 1000.00, "count": 10},
        "month": {"amount": 15000.00, "count": 150},
        "account_type_stats": [
            {"type": 1, "type_name": "微信", "amount": 30000.00, "count": 300}
        ]
    }
}
```

## 