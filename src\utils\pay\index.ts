import { Pay } from './pay'
import WechatPay from './wechat'
import Alipay from './alipay'
import Transfer from './transfer'
export { PayWayEnum } from './pay'
export function createdPay() {
    const pay = new Pay()
    const wechat = new WechatPay()
    const alipay = new Alipay()
    const transfer = new Transfer()
    pay.inject('WechatPay', wechat).inject('Alipay', alipay).inject('Transfer', transfer)
    return pay
}
