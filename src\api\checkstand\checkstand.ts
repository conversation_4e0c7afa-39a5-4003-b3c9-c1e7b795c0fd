import request from '@/plugins/axios'
import * as Interface from '@/api/user/user.d.ts'

// 理发师列表   所有模块直接搜索理发师名字
/*
示例：
    {
    "code": 1,
    "show": 0,
    "msg": "",
    "data": [
        {
            "id": 8,
            "name": "托尼老师",             "理发师名字"
            "shop_id": 2,
            "jid": 4,
            "shop_name": "测试一门店二",    "店铺名字"
            "job_name": "111"               "职位名称"
        },
        {
            "id": 19,
            "name": "十七",
            "shop_id": 5,
            "jid": 2,
            "shop_name": "十七",
            "job_name": "助理"
        }
    ]
}

Query 参数：
        name string 名称

*/

export const apiCheckstandList = (params: any) => request.get('/cashier/barber_list', { params })


// 服务内容
/*
示例：
    {
    "code": 1,
    "show": 0,
    "msg": "",
    "data": [
        {
            "id": 2,
            "services_id": 8,
            "price": "15",                  "价格"
            "vip_price": "10",              "会员价格"
            "points": "10",                 "积分"
            "points_price": "5",            "积分价格"
            "services_title": "洗头发"      "服务项目"
        },
        {
            "id": 11,
            "services_id": 6,
            "price": "123456",
            "vip_price": "123456",
            "points": "123456",
            "points_price": "123456",
            "services_title": "剪头发"
        }
    ]
}

Query 参数：
        bid string  理发师id

*/

export const apiCheckstandServiceList = (params: any) => request.get('/cashier/service_list', { params })


// 剪发用户
/*
示例：
    {
    "code": 1,
    "show": 0,
    "msg": "",
    "data": [
        {
            "vip_id": 6,
            "uid": 73,
            "mobile": "13131886721",
            "state": 1,
            "points": 0,
            "user_id": 73,
            "user_sn": "67906408",
            "nickname": "DS550067#2480",
            "avatar": "https://ceshiyi.yusengkeji.cn/uploads/2yjp63v5/user/images/20250510/20250510193159441614174.jpeg",
            "sex": 0,
            "name": "DS550067#2480",
            "sex_text": "未知",
            "level_info": {
                "level_id": 6,
                "level_name": "黄金会员",
                "level_rank": 2,
                "level_discount": 5
            }
        }
    ]
}

Query 参数：
    mobile 手机号
*/


export const apiCheckstandUserList = (params: any) => request.get('/cashier/user', { params })

// 用户名下会员卡
/*
示例：
    {
    "code": 1,
    "show": 0,
    "msg": "",
    "data": [
        {
            "id": 1,
            "vuid": 2,
            "price": "100",                 "总余额"
            "used_price": "0",              "已用余额"
            "remain_price": "100",          "剩余月"
            "title": "",                    "卡片名称"
            "num": "",                      "总次数"
            "used_num": "",                 "已用次数"
            "remain_num": "",               "剩余次数"
            "create_time": 1747036171,
            "type": 1                       "卡片类型 1会员卡 2次卡"
        },
        {
            "id": 2,
            "vuid": 2,
            "price": "",
            "used_price": "",
            "remain_price": "",
            "title": "剪发卡",
            "num": "10",
            "used_num": "0",
            "remain_num": "10",
            "create_time": 1747036231,
            "type": 2
        },
        {
            "id": 3,
            "vuid": 2,
            "price": "",
            "used_price": "",
            "remain_price": "",
            "title": "染发卡",
            "num": "10",
            "used_num": "0",
            "remain_num": "10",
            "create_time": 1747036269,
            "type": 2
        },
        {
            "id": 4,
            "vuid": 2,
            "price": "",
            "used_price": "",
            "remain_price": "",
            "title": "十七",
            "num": "17",
            "used_num": "0",
            "remain_num": "17",
            "create_time": 1747114474,
            "type": 2
        }
    ]
}
Query 参数：
        vuid ：会员ID
*/

export const apiCheckstandUserCardList = (params: any) => request.get('/cashier/user_card_list', { params })

// 搜索商品
/*
示例：
{
    "code": 1,
    "show": 0,
    "msg": "",
    "data": [
        {
            "id": 4,
            "name": "店长洗剪吹",       // "商品名称"
            "image": "https://ceshiyi.yusengkeji.cn/uploads/cv9nf260/admin/images/20250412/20250412113306c71870152.jpeg",
            "item": [
                {
                    "id": 9,
                    "goods_id": 4,
                    "sell_price": "50.00",  // "金额"
                    "spec_value_str": "默认"    // "商品规格"
                }
            ]
        }
    ]
}

Query 参数：
        name 商品名称

*/

export const apiCheckstandGoodsList = (params: any) => request.get('/cashier/merchant_shop_lists', { params })


/*
{
    "code": 1,
    "msg": "success",
    "data": {
        "categories": [
            {
                "key": "all",
                "name": "全部",
                "count": 25
            },
            {
                "key": "goods",
                "name": "商品",
                "count": 15
            },
            {
                "key": "service",
                "name": "服务项目",
                "count": 10
            }
        ],
        "items": [
            {
                "id": 1,
                "title": "专业洗发水",
                "desc": "进口天然成分洗发水",
                "image": "https://example.com/image.jpg",
                "original_price": 100.00,
                "current_price": 85.00,
                "member_price": 85.00,
                "is_member_price": 1,
                "vip_price": 85.00,
                "stock": 50,
                "item_type": "goods",
                "category_name": "商品"
            },
            {
                "id": 2,
                "title": "经典洗剪吹",
                "desc": "专业理发师洗剪吹服务",
                "image": "https://example.com/service.jpg",
                "original_price": 80.00,
                "current_price": 70.00,
                "member_price": 70.00,
                "is_member_price": 1,
                "vip_price": 70.00,
                "points": 10,
                "points_price": 5.00,
                "stock": 999,
                "item_type": "service",
                "category_name": "服务项目"
            }
        ]
    }
}
*/
/*
* 请求参数
* uid：会员用户ID，传入后计算会员价格
*
* 请求参数：
* uid：会员的vip_id，传入后根据会员等级计算会员价格
* name：商品名称（搜索时使用）
* */

export const apiShopList = (params: any) => request.get('/cashier/merchant_shop_lists', { params })


// 支付
/*
* Body 参数：
*   bid：理发师id
*   service_title：选择的服务 （多个英文逗号分割）
*   shampoo_bid：洗头服务选择的理发师
*   price：金额
*   pay_type：支付方式：1-现金 2-微信 3-支付宝 4-银行卡  6-会员卡余额 7-会员卡次数 8-抖音支付  9-美团支付
*   uid：用户id 0为散客
*   card_id：会员卡id
*   open_card_state：是否开新卡 1是 不传就是不开
*   order_items：订单项目列表
* */

export const apiCheckstandPay = (params: any) => request.post('/cashier_order/checkout', params)


/*
{
    "code": 1,
    "msg": "成功",
    "data": {
        "lists": [
            {
                "id": 1,
                "name": "普通用户",
                "rank": 1,
                "image": "https://example.com/images/level1.png",
                "discount": "无",
                "num": 1256
            },
            {
                "id": 2,
                "name": "黄金会员",
                "rank": 2,
                "image": "https://example.com/images/level2.png",
                "discount": "9.5折",
                "num": 86
            }
        ],
        "count": 5,
        "page_no": 1,
        "page_size": 10
    },
    "show": 1,
    "url": ""
}
* */


// 获取用户等级列表
export const apiUserLevelList = (params: any): Promise<Interface.LevelLists_Res> =>
    request.get('/user.user_level/lists', { params })
