<template>
    <div class="ls-order">
        <div class="ls-order__top ls-card">
            <div class="flex">
                当前套餐：<span class="weight-500">{{ setMeal.set_meal_name }}</span>
                <ls-dialog
                    width="800px"
                    top="20vh"
                    ref="dialog"
                    :title="setMeal.set_meal_name"
                    cancelButtonText=""
                    confirmButtonText=""
                >
                    <template slot="trigger">
                        <i slot="reference" class="el-icon-question pointer m-l-5"></i>
                    </template>
                    <template>
                        <div>
                            <div class="weight-500" v-if="setMeal.meal_module.marketing.length">营销功能</div>
                            <div class="flex" style="flex-wrap: wrap" v-if="setMeal.meal_module.marketing.length">
                                <div v-for="item in setMeal.meal_module.marketing" class="carditem" :key="item.id">
                                    {{ item.name }}
                                </div>
                            </div>
                            <div class="weight-500 m-t-20" v-if="setMeal.meal_module.apply.length">应用功能</div>
                            <div class="flex" style="flex-wrap: wrap" v-if="setMeal.meal_module.apply.length">
                                <div v-for="item in setMeal.meal_module.apply" class="carditem" :key="item.id">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </template>
                </ls-dialog>
            </div>
            <div class="m-t-10">到期时间：{{ setMeal.expires_time }}</div>
        </div>

        <div class="ls-order__table ls-card m-t-16">
            <router-link to="/setting/shop/renew/place_order">
                <el-button type="primary" size="mini">立即续费</el-button>
            </router-link>
            <div class="pane-table m-t-16">
                <el-table :data="pager.lists" ref="paneTable" style="width: 100%" size="mini" v-loading="pager.loading">
                    <el-table-column label="订单编号" prop="sn" width="150"></el-table-column>
                    <el-table-column label="套餐名称" prop="set_meal_name" width="150"></el-table-column>
                    <el-table-column prop="time_desc" label="套餐时长" min-width="80"></el-table-column>
                    <el-table-column label="支付金额" min-width="80">
                        <template slot-scope="scope">
                            <span>¥{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="pay_way_desc" label="支付方式" min-width="100"></el-table-column>
                    <!-- 订单的状态 -->
                    <el-table-column label="支付状态" prop="pay_status_desc" min-width="80">
                        <template slot-scope="scope">
                            <span :class="{ danger: scope.row.pay_status == 0 }">{{ scope.row.pay_status_desc }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单状态" prop="order_status_desc" min-width="80">
                        <template slot-scope="scope">
                            <span :class="{ danger: scope.row.order_status == 0 }">{{
                                scope.row.order_status_desc
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="下单时间" prop="create_time" min-width="120"></el-table-column>
                    <!-- 操作 -->
                    <el-table-column fixed="right" label="操作" width="120">
                        <template slot-scope="scope">
                            <div class="flex">
                                <!-- 详情 -->
                                <el-button @click="toOrder(scope.row.id)" type="text" size="small"> 详情 </el-button>
                                <el-button
                                    v-if="scope.row.order_status == 0"
                                    size="mini"
                                    type="text"
                                    @click="confirmPay(scope.row.id)"
                                    >继续支付</el-button
                                >
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pane-footer m-t-16 flex row-right">
                <ls-pagination v-model="pager" @change="getOrderLists()" />
            </div>
        </div>
        <pay-select v-model="showPay" :data="payWays" class="m-l-10" @confirm="pay($event)"> </pay-select>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import DatePicker from '@/components/date-picker.vue'
import { apiPayWays, apiRenewLists, apiRenewPay, apiRenewPayStatus, apiRenewSetMeal } from '@/api/setting/renew'
import ExportData from '@/components/export-data/index.vue'
import LsPagination from '@/components/ls-pagination.vue'
import { RequestPaging } from '@/utils/util'
import PaySelect from '@/components/pay-select/dialog.vue'
import { createdPay } from '@/utils/pay'
import payMixin from '@/mixin/pay'
import { rotationCreate } from '@/utils/rotation'
import LsDialog from '@/components/ls-dialog.vue'

@Component({
    components: {
        DatePicker,
        ExportData,
        LsPagination,
        PaySelect,
        LsDialog
    },
    mixins: [payMixin]
})
export default class Order extends Vue {
    orderId: number | undefined
    pager = new RequestPaging()
    setMeal: any = {}
    showPay = false
    // E Data

    // S Methods

    // 获取订单信息
    getOrderLists(page?: number) {
        page && (this.pager.page = page)

        this.pager.request({
            callback: apiRenewLists
        })
    }
    async getSetMeal() {
        const res = await apiRenewSetMeal()
        this.setMeal = res
    }

    // 去订单详情
    toOrder(id: any) {
        this.$router.push({
            path: '/setting/shop/renew/detail',
            query: { id }
        })
    }
    payStatus = rotationCreate(this.checkPay)
    confirmPay(id: number) {
        this.orderId = id
        this.showPay = true
    }
    async pay(payWay: number) {
        try {
            const res = await apiRenewPay({
                pay_way: payWay,
                order_id: this.orderId,
                redirect_url: '/admin/setting/shop/renew'
            })
            const pay = createdPay()
            const payModule = pay.getPayModules(payWay)
            this.payStatus.start()
            await payModule.pay(res, this.$createElement)
        } catch (error) {
            this.payStatus.stop()
            this.getOrderLists()
        }
    }
    async checkPay(stop: any) {
        const res = await apiRenewPayStatus({ order_id: this.orderId })
        if (res.pay_status == 1) {
            stop()
            this.getOrderLists()
            this.$msgbox.close()
        }
    }
    beforeDestroy() {
        this.payStatus.stop()
    }
    // E Methods

    // S  life cycle

    created() {
        // 获取订单信息
        this.getOrderLists()
        this.getSetMeal()
    }

    // E life cycle
}
</script>

<style lang="scss" scoped>
.carditem {
    width: 95px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid rgba(64, 115, 250, 1);
    text-align: center;
    line-height: 36px;
    margin-left: 10px;
    margin-top: 10px;
    color: rgba(64, 115, 250, 1);
}
</style>
