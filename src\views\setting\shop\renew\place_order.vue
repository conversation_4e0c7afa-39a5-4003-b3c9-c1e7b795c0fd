<template>
    <div>
        <div class="ls-card">
            <el-page-header @back="$router.go(-1)" content="套餐续费" />
        </div>
        <div class="ls-card m-t-16">
            <el-form size="mini">
                <div class="lighter m-b-16">请选择合适的套餐</div>
                <div class="meal-list flex flex-wrap">
                    <div
                        class="meal-item"
                        v-for="item in renewInfo.meal_prices"
                        :key="item.id"
                        :class="{ active: item.id == activeMeal.id }"
                        @click="activeMeal = item"
                    >
                        <div class="nr">{{ item.time_desc }}</div>
                        <div class="primary nr">
                            ¥
                            <span class="font-size-20"> {{ item.price }} </span>
                        </div>
                    </div>
                </div>
                <el-form-item label="套餐名称">
                    {{ renewInfo.meal_name }}
                </el-form-item>
                <el-form-item label="套餐时长">
                    {{ activeMeal.time_desc }}
                </el-form-item>
                <el-form-item label="套餐价格"> ¥{{ activeMeal.price }} </el-form-item>
                <pay-select v-model="orderForm.pay_way" :data="renewInfo.pay_ways" />
                <el-form-item>
                    <el-button type="primary" size="mini" @click="placeOrder">提交订单</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import LsDialog from '@/components/ls-dialog.vue'
import PaySelect from '@/components/pay-select/detail.vue'
import { apiRenewPlaceOrder, apiRenewInfo, apiRenewPayStatus } from '@/api/setting/renew'
import { throttle } from '@/utils/util'
import { createdPay } from '@/utils/pay'
import { rotationCreate } from '@/utils/rotation'
@Component({
    components: {
        LsDialog,
        PaySelect
    }
})
export default class PlaceOrder extends Vue {
    orderId: number | undefined
    renewInfo: any = {
        meal_name: '',
        meal_prices: [],
        pay_ways: {}
    }
    activeMeal: any = {}
    orderForm = {
        pay_way: 0,
        set_meal_price_id: 0,
        redirect_url: '/admin/setting/shop/renew'
    }
    payStatus = rotationCreate(this.checkPay)
    async getRenewInfo() {
        const res = await apiRenewInfo()
        this.renewInfo = res
        this.activeMeal = res.meal_prices.at(0) || {}
    }
    async placeOrder() {
        try {
            this.orderForm.set_meal_price_id = this.activeMeal.id
            const res = await apiRenewPlaceOrder(this.orderForm)
            this.orderId = res.order_id
            const pay = createdPay()
            const payModule = pay.getPayModules(res.pay_way)
            this.payStatus.start()
            await payModule.pay(res, this.$createElement)
        } catch (error) {
            console.log(error)
            this.payStatus.stop()
            this.$router.replace('/setting/shop/renew')
        }
    }
    async checkPay(stop: any) {
        const res = await apiRenewPayStatus({ order_id: this.orderId })
        if (res.pay_status == 1) {
            stop()
            this.$msgbox.close()
            this.$router.replace('/setting/shop/renew')
        }
    }
    created() {
        this.getRenewInfo()
        this.placeOrder = throttle(this.placeOrder, 1000) as any
    }
    beforeDestroy() {
        this.payStatus.stop()
    }
}
</script>

<style lang="scss" scoped>
.meal-list {
    .meal-item {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        line-height: 1.5;
        width: 120px;
        height: 80px;
        margin-right: 20px;
        margin-bottom: 10px;
        border-radius: 6px;
        border: 1px solid $--border-color-base;

        &.active {
            border-color: $--color-primary;
        }
    }
}
</style>
