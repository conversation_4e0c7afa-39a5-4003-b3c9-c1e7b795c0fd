<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
    apiBarberList,
    apiBarberProjectList,
    apiBarberProjectSave,
    apiBarberProjectDel,
    apiProjectList
} from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'

@Component({
    name: 'BarberProject',
    components: {
        LsPagination
    }
})
export default class BarberProject extends Vue {
    // 当前选中的理发师
    private currentBarber = {
        id: '',
        name: ''
    }

    // 理发师列表
    private barberList: any[] = []

    // 理发师已有项目列表
    private projectList: any[] = []

    // 所有可选项目列表
    private allProjectList: any[] = []

    // 分页数据
    private pager = new RequestPaging()

    // 项目弹窗
    private projectDialog = {
        visible: false,
        title: '选择项目',
        isEdit: false
    }

    // 项目表单
    private projectForm = {
        id: '', // 理发师项目关联ID
        barber_id: '', // 理发师ID
        services_id: '', // 项目服务ID
        title: '', // 项目名称（显示用）
        price: '', // 项目价格
        vip_price: '', // 会员价格
        points: '', // 积分
        points_price: '' // 积分价格
    }

    // 项目表单规则
    private projectRules = {
        services_id: [{ required: true, message: '请选择项目', trigger: 'change' }],
        price: [{ required: true, message: '请输入项目价格', trigger: 'blur' }]
    }

    // 搜索理发师关键词
    private barberSearchKeyword = ''

    // 加载状态
    private loading = false

    created() {
        this.getBarberList()
        this.getAllProjectList()
    }

    // 获取理发师列表
    async getBarberList() {
        try {
            this.loading = true
            const res = await apiBarberList({
                page_no: 1,
                page_size: 10000
            })
            this.barberList = res.lists
            // 默认选中第一个理发师
            this.handleSelectBarber(this.barberList[0])
        } catch (error) {
            console.error('获取理发师列表失败', error)
        } finally {
            this.loading = false
        }
    }

    // 获取所有可选项目列表
    async getAllProjectList() {
        try {
            this.loading = true
            const res = await apiProjectList({})
            console.log('1111项目列表：', res)
            this.allProjectList = res
        } catch (error) {
            console.error('获取项目列表失败', error)
        } finally {
            this.loading = false
        }
    }

    // 选择理发师
    handleSelectBarber(barber: any) {
        console.log('选择理发师：', barber)

        this.currentBarber = {
            id: barber.id,
            name: barber.name
        }
        this.getProjectList()
    }

    // 获取理发师项目列表
    async getProjectList() {
        if (!this.currentBarber.id) {
            return
        }

        try {
            this.loading = true
            const res = await apiBarberProjectList({ id: this.currentBarber.id })
            console.log('理发师项目列表：', res)
            if (res && res.lists) {
                this.projectList = res.lists
            } else {
                this.projectList = []
            }
        } catch (error) {
            console.error('获取理发师项目列表失败', error)
            this.projectList = []
        } finally {
            this.loading = false
        }
    }

    // 打开选择项目弹窗
    handleAddProject() {
        this.projectDialog = {
            visible: true,
            title: '选择项目',
            isEdit: false
        }
        this.projectForm = {
            id: '',
            barber_id: this.currentBarber.id,
            services_id: '',
            title: '',
            price: '',
            vip_price: '',
            points: '',
            points_price: ''
        }
    }

    // 打开编辑项目弹窗
    handleEditProject(project: any) {
        this.projectDialog = {
            visible: true,
            title: '编辑项目价格',
            isEdit: true
        }
        this.projectForm = {
            id: project.id,
            barber_id: this.currentBarber.id,
            services_id: project.services_id || project.id, // 兼容不同返回格式
            title: project.title,
            price: project.price,
            vip_price: project.vip_price,
            points: project.points,
            points_price: project.points_price
        }
    }

    // 删除项目
    async handleDeleteProject(project: any) {
        try {
            await this.$confirm('确认删除该项目关联?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })

            this.loading = true
            try {
                await apiBarberProjectDel({ id: project.id })
                this.$message.success('删除成功')
                this.getProjectList()
            } catch (error) {
                console.error('删除项目失败', error)
                this.$message.error('删除失败')
            }
        } catch (error) {
            console.log('取消删除')
        } finally {
            this.loading = false
        }
    }

    // 保存项目
    async handleSaveProject() {
        const formRef = this.$refs.projectForm as any
        if (!formRef) {
            return
        }

        try {
            await formRef.validate()

            this.loading = true
            try {
                const params = {
                    id: this.projectForm.id, // 如果是编辑，传递id
                    barber_id: this.currentBarber.id,
                    services_id: this.projectForm.services_id,
                    price: this.projectForm.price,
                    vip_price: this.projectForm.vip_price,
                    points: this.projectForm.points,
                    points_price: this.projectForm.points_price
                }

                await apiBarberProjectSave(params)
                this.$message.success(this.projectDialog.isEdit ? '修改成功' : '新增成功')
                this.projectDialog.visible = false
                this.getProjectList()
            } catch (error) {
                console.error('保存项目失败', error)
                this.$message.error(this.projectDialog.isEdit ? '修改失败' : '新增失败')
            }
        } catch (error) {
            console.error('表单验证失败', error)
        } finally {
            this.loading = false
        }
    }

    // 关闭弹窗
    handleCloseDialog() {
        this.projectDialog.visible = false
    }

    // 处理理发师搜索
    handleBarberSearch() {
        if (!this.barberSearchKeyword) {
            return
        }

        const filtered = this.barberList.filter(item =>

            item.name.includes(this.barberSearchKeyword) ||
            (item.mobile && item.mobile.includes(this.barberSearchKeyword))

        )
        console.log('filtered', filtered)
        if (filtered.length > 0) {
            this.handleSelectBarber(filtered[0])
        }
    }

    // 处理项目选择改变
    handleProjectChange(servicesId: string) {
        // 设置项目名称，仅用于显示
        const selectedProject = this.allProjectList.find(item => item.id == servicesId)
        if (selectedProject) {
            this.projectForm.title = selectedProject.title
        }
    }

    // 检查项目是否已添加
    isProjectAdded(servicesId: string): boolean {
        return this.projectList.some(item =>
            item.services_id == servicesId || item.id == servicesId
        )
    }

    // 获取可选项目列表(排除已添加的)
    get availableProjects() {
        if (!this.allProjectList.length) {
            return []
        }

        return this.allProjectList.filter(item =>
            !this.isProjectAdded(item.id)
        )
    }
}
</script>

<template>
    <div class="barber-project-container" v-loading="loading">
        <!-- 页面标题 -->
        <div class="page-title">理发师项目管理</div>

        <div class="barber-project-content">
            <!-- 左侧理发师列表 -->
            <div class="barber-list-panel">
                <div class="panel-title">理发师列表</div>
                <el-input v-model="barberSearchKeyword" placeholder="搜索理发师" prefix-icon="el-icon-search"
                    class="barber-search" clearable @keyup.enter.native="handleBarberSearch">
                    <el-button slot="append" icon="el-icon-search" @click="handleBarberSearch"></el-button>
                </el-input>
                <div class="barber-items">
                    <div v-for="item in barberList" :key="item.id" class="barber-item"
                        :class="{ 'active': currentBarber.id === item.id }" @click="handleSelectBarber(item)">
                        <el-avatar :src="item.image" :size="36"></el-avatar>
                        <div class="barber-name">{{ item.name }}</div>
                    </div>
                    <div v-if="barberList.length === 0" class="empty-tip">
                        暂无理发师
                    </div>
                </div>
            </div>

            <!-- 右侧项目列表 -->
            <div class="project-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        {{ currentBarber.name ? `${currentBarber.name}的项目` : '请选择理发师' }}
                    </div>
                    <el-button type="primary" size="small" icon="el-icon-plus"
                        :disabled="!currentBarber.id || !availableProjects.length" @click="handleAddProject">
                        选择项目
                    </el-button>
                </div>

                <!-- 项目表格 -->
                <el-table :data="projectList" border style="width: 100%" size="mini" v-if="currentBarber.id">
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="title" label="项目名称"></el-table-column>
                    <el-table-column prop="price" label="价格">
                        <template slot-scope="scope">
                            ¥{{ scope.row.price }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="vip_price" label="会员价格">
                        <template slot-scope="scope">
                            ¥{{ scope.row.vip_price }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="points" label="积分">
                        <template slot-scope="scope">
                            {{ scope.row.points }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="points_price" label="积分价格">
                        <template slot-scope="scope">
                            ¥{{ scope.row.points_price }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="handleEditProject(scope.row)">修改价格</el-button>
                            <el-button type="text" size="small" @click="handleDeleteProject(scope.row)"
                                class="delete-btn">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 未选择理发师或无项目时的提示 -->
                <div class="empty-project" v-if="currentBarber.id && projectList.length === 0">
                    <el-empty description="暂无项目"></el-empty>
                </div>
                <div class="empty-project" v-if="!currentBarber.id">
                    <el-empty description="请先选择理发师"></el-empty>
                </div>
            </div>
        </div>

        <!-- 项目表单弹窗 -->
        <el-dialog :title="projectDialog.title" :visible.sync="projectDialog.visible" width="500px"
            :close-on-click-modal="false" @closed="handleCloseDialog">
            <el-form ref="projectForm" :model="projectForm" :rules="projectRules" label-width="100px" size="small">
                <el-form-item label="项目名称" prop="services_id" v-if="!projectDialog.isEdit">
                    <el-select v-model="projectForm.services_id" placeholder="请选择项目" filterable
                        @change="handleProjectChange" style="width: 100%">
                        <el-option v-for="item in availableProjects" :key="item.id" :label="item.title"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="项目名称" v-if="projectDialog.isEdit">
                    <div class="form-text">{{ projectForm.title }}</div>
                </el-form-item>

                <el-form-item label="项目价格" prop="price">
                    <el-input v-model="projectForm.price" clearable placeholder="请输入项目价格" type="number">
                        <template slot="prepend">¥</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="会员价格" prop="vip_price">
                    <el-input v-model="projectForm.vip_price" clearable placeholder="请输入会员价格" type="number">
                        <template slot="prepend">¥</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="积分" prop="points">
                    <el-input v-model="projectForm.points" clearable placeholder="请输入积分" type="number"></el-input>
                </el-form-item>
                <el-form-item label="积分价格" prop="points_price">
                    <el-input v-model="projectForm.points_price" clearable placeholder="请输入积分价格" type="number">
                        <template slot="prepend">¥</template>
                    </el-input>
                </el-form-item>

            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="projectDialog.visible = false">取 消</el-button>
                <el-button type="primary" @click="handleSaveProject" :loading="loading">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.barber-project-container {
    padding: 20px;

    .page-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .barber-project-content {
        display: flex;
        background-color: #fff;
        border-radius: 4px;

        .barber-list-panel {
            width: 250px;
            border-right: 1px solid #ebeef5;
            padding: 15px;

            .panel-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
            }

            .barber-search {
                margin-bottom: 15px;
            }

            .barber-items {
                max-height: 600px;
                overflow-y: auto;

                .barber-item {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    cursor: pointer;
                    border-radius: 4px;
                    margin-bottom: 5px;

                    &:hover {
                        background-color: #f5f7fa;
                    }

                    &.active {
                        background-color: #ecf5ff;
                    }

                    .barber-name {
                        margin-left: 10px;
                        font-size: 14px;
                    }
                }

                .empty-tip {
                    text-align: center;
                    color: #909399;
                    padding: 20px 0;
                }
            }
        }

        .project-panel {
            flex: 1;
            padding: 15px;

            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;

                .panel-title {
                    font-size: 16px;
                    font-weight: bold;
                }
            }

            .empty-project {
                margin-top: 50px;
            }
        }
    }

    .delete-btn {
        color: #F56C6C;
    }

    .form-text {
        line-height: 32px;
        font-size: 14px;
        color: #606266;
    }
}
</style>