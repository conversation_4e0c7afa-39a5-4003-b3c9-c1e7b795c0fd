<template>
    <div class="withdraw-list">
        <!-- 搜索筛选 -->
        <div class="search-container">
            <el-card>
                <el-form :model="searchForm" inline>
                    <el-form-item label="用户昵称">
                        <el-input
                            v-model="searchForm.nickname"
                            placeholder="请输入用户昵称"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="手机号">
                        <el-input
                            v-model="searchForm.mobile"
                            placeholder="请输入手机号"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="请选择状态"
                            clearable
                            style="width: 150px"
                        >
                            <el-option label="待审核" :value="0" />
                            <el-option label="审核通过" :value="1" />
                            <el-option label="审核拒绝" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <el-card>
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column prop="id" label="申请ID" width="80" />
                    <el-table-column prop="nickname" label="用户昵称" min-width="120" />
                    <el-table-column prop="mobile" label="手机号" width="130" />
                    <el-table-column prop="amount" label="提现金额" width="120">
                        <template slot-scope="scope">
                            ¥{{ scope.row.amount }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="account_type_name" label="提现方式" width="100" />
                    <el-table-column prop="status_name" label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag 
                                :type="getStatusType(scope.row.status_name)"
                            >
                                {{ scope.row.status_name }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="create_time" label="申请时间" width="160" />
                    <el-table-column prop="audit_time" label="审核时间" width="160" />
                    <el-table-column prop="audit_admin_name" label="审核人" width="120" />
                    <el-table-column label="操作" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.status_name === '待审核'"
                                type="text"
                                size="small"
                                @click="handleAudit(scope.row, 1)"
                            >
                                通过
                            </el-button>
                            <el-button
                                v-if="scope.row.status_name === '待审核'"
                                type="text"
                                size="small"
                                class="danger"
                                @click="handleAudit(scope.row, 2)"
                            >
                                拒绝
                            </el-button>
                            <span v-if="scope.row.status_name !== '待审核'">-</span>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="pagination.page"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pagination.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total"
                    />
                </div>
            </el-card>
        </div>

        <!-- 审核对话框 -->
        <el-dialog
            :title="auditDialog.title"
            :visible.sync="auditDialog.visible"
            width="500px"
        >
            <el-form :model="auditForm" label-width="80px">
                <el-form-item label="审核备注">
                    <el-input
                        v-model="auditForm.remark"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入审核备注"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="auditDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="confirmAudit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { 
    apiDistributionWithdrawList, 
    apiDistributionWithdrawAudit 
} from '@/api/distribution_promotion/distribution_promotion'
import * as Interface from '@/api/distribution_promotion/distribution_promotion.d'

@Component({
    name: 'WithdrawList'
})
export default class WithdrawList extends Vue {
    // 搜索表单
    private searchForm: Interface.WithdrawListReq = {
        nickname: '',
        mobile: '',
        status: undefined,
        page: 1,
        limit: 20
    }

    // 表格数据
    private tableData: Interface.WithdrawApplication[] = []

    // 分页信息
    private pagination = {
        page: 1,
        limit: 20,
        total: 0
    }

    // 加载状态
    private loading = false

    // 审核对话框
    private auditDialog = {
        visible: false,
        title: '',
        currentRow: null as Interface.WithdrawApplication | null,
        status: 0
    }

    // 审核表单
    private auditForm = {
        remark: ''
    }

    created() {
        this.getWithdrawList()
    }

    // 获取提现列表
    async getWithdrawList() {
        try {
            this.loading = true
            const params = {
                ...this.searchForm,
                page: this.pagination.page,
                limit: this.pagination.limit
            }
            const res = await apiDistributionWithdrawList(params)
            if (res && res.list) {
                this.tableData = res.list
                this.pagination.total = res.total || 0
            }
        } catch (error) {
            console.error('获取提现列表失败', error)
            this.$message.error('获取提现列表失败')
        } finally {
            this.loading = false
        }
    }

    // 搜索
    handleSearch() {
        this.pagination.page = 1
        this.getWithdrawList()
    }

    // 重置
    handleReset() {
        this.searchForm = {
            nickname: '',
            mobile: '',
            status: undefined,
            page: 1,
            limit: 20
        }
        this.pagination.page = 1
        this.getWithdrawList()
    }

    // 审核
    handleAudit(row: Interface.WithdrawApplication, status: number) {
        this.auditDialog.visible = true
        this.auditDialog.title = status === 1 ? '审核通过' : '审核拒绝'
        this.auditDialog.currentRow = row
        this.auditDialog.status = status
        this.auditForm.remark = ''
    }

    // 确认审核
    async confirmAudit() {
        if (!this.auditDialog.currentRow) return

        try {
            await apiDistributionWithdrawAudit({
                id: this.auditDialog.currentRow.id,
                status: this.auditDialog.status,
                remark: this.auditForm.remark
            })

            this.$message.success('审核成功')
            this.auditDialog.visible = false
            this.getWithdrawList()
        } catch (error) {
            console.error('审核失败', error)
            this.$message.error('审核失败')
        }
    }

    // 获取状态类型
    getStatusType(statusName: string) {
        switch (statusName) {
            case '待审核':
                return 'warning'
            case '审核通过':
                return 'success'
            case '审核拒绝':
                return 'danger'
            default:
                return 'info'
        }
    }

    // 分页大小改变
    handleSizeChange(val: number) {
        this.pagination.limit = val
        this.pagination.page = 1
        this.getWithdrawList()
    }

    // 当前页改变
    handleCurrentChange(val: number) {
        this.pagination.page = val
        this.getWithdrawList()
    }
}
</script>

<style lang="scss" scoped>
.withdraw-list {
    padding: 20px;

    .search-container {
        margin-bottom: 20px;
    }

    .table-container {
        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    }

    .el-button.danger {
        color: #f56c6c;
    }
}
</style>
