<template>
    <el-dialog
        :visible.sync="visible"
        title="支付成功"
        width="500px"
        top="15vh"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="slip-dialog">
            <!-- 成功图标 -->
            <div class="success-icon">
                <i class="el-icon-success"></i>
                <h3>支付成功！</h3>
            </div>

            <!-- 水单信息 -->
            <div class="slip-info" v-if="slipData">
                <div class="slip-header">
                    <h4>水单信息</h4>
                    <div class="slip-no">水单号：{{ slipData.slip_no }}</div>
                </div>

                <div class="slip-content">
                    <!-- 基本信息 -->
                    <div class="info-row">
                        <span class="label">理发师：</span>
                        <span class="value">{{ slipData.barber_name }}</span>
                    </div>

                    <div class="info-row" v-if="slipData.customer_name && slipData.customer_name.trim()">
                        <span class="label">客户：</span>
                        <span class="value">{{ slipData.customer_name }}</span>
                    </div>

                    <!-- 服务项目 -->
                    <div class="info-row" v-if="statistics && statistics.service_items && statistics.service_items.length > 0">
                        <span class="label">服务项目：</span>
                        <span class="value">{{ statistics.service_items.join('、') }}</span>
                    </div>

                    <!-- 商品项目 -->
                    <div class="info-row" v-if="statistics && statistics.goods_items && statistics.goods_items.length > 0">
                        <span class="label">商品：</span>
                        <span class="value">{{ statistics.goods_items.join('、') }}</span>
                    </div>

                    <!-- 金额信息 -->
                    <div class="amount-section">
                        <div class="info-row">
                            <span class="label">总金额：</span>
                            <span class="value amount">¥{{ slipData.total_amount }}</span>
                        </div>

                        <div class="info-row" v-if="slipData.discount_amount > 0">
                            <span class="label">优惠金额：</span>
                            <span class="value discount">-¥{{ slipData.discount_amount }}</span>
                        </div>

                        <div class="info-row final-amount">
                            <span class="label">实付金额：</span>
                            <span class="value amount">¥{{ slipData.final_amount }}</span>
                        </div>
                    </div>

                    <!-- 支付信息 -->
                    <div class="info-row">
                        <span class="label">支付方式：</span>
                        <span class="value">{{ slipData.pay_type_name }}</span>
                    </div>

                    <!-- 余额信息 -->
                    <div class="info-row" v-if="slipData.remaining_balance > 0">
                        <span class="label">剩余余额：</span>
                        <span class="value">¥{{ slipData.remaining_balance }}</span>
                    </div>

                    <!-- 结算时间 -->
                    <div class="info-row">
                        <span class="label">结算时间：</span>
                        <span class="value">{{ slipData.checkout_time }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
            <el-button type="primary" @click="handlePrint" v-if="showPrintButton">打印小票</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

interface SlipData {
    slip_no: string
    slip_id: number
    total_amount: number
    discount_amount: number
    final_amount: number
    pay_type_name: string
    remaining_balance: number
    barber_name: string
    customer_name?: string
    checkout_time: string
}

interface Statistics {
    service_items: string[]
    goods_items: string[]
    service_count: number
    goods_count: number
    total_items: number
    service_amount: number
    goods_amount: number
}

@Component({
    name: 'SlipDialog'
})
export default class SlipDialog extends Vue {
    @Prop({ default: false }) value!: boolean
    @Prop({ default: null }) slipData!: SlipData | null
    @Prop({ default: null }) statistics!: Statistics | null
    @Prop({ default: false }) showPrintButton!: boolean

    get visible() {
        return this.value
    }

    set visible(val: boolean) {
        this.$emit('input', val)
    }

    handleClose() {
        this.visible = false
        this.$emit('close')
    }

    handlePrint() {
        this.$emit('print', this.slipData)
        // 这里可以添加打印逻辑
        this.$message.info('打印功能待开发')
    }


}
</script>

<style scoped lang="scss">
.slip-dialog {
    text-align: center;

    .success-icon {
        margin-bottom: 20px;

        i {
            font-size: 48px;
            color: #67c23a;
            margin-bottom: 10px;
        }

        h3 {
            margin: 0;
            color: #67c23a;
            font-weight: 500;
        }
    }

    .slip-info {
        text-align: left;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;

        .slip-header {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;

            h4 {
                margin: 0 0 5px 0;
                color: #333;
                font-size: 16px;
            }

            .slip-no {
                color: #666;
                font-size: 14px;
            }
        }

        .slip-content {
            .info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .label {
                    color: #666;
                    font-size: 14px;
                    min-width: 80px;
                }

                .value {
                    color: #333;
                    font-size: 14px;
                    font-weight: 500;
                    text-align: right;
                    flex: 1;

                    &.amount {
                        color: #e6a23c;
                        font-weight: 600;
                    }

                    &.discount {
                        color: #67c23a;
                    }
                }

                &.final-amount {
                    background: #fff;
                    margin: 10px -10px 0;
                    padding: 12px 10px;
                    border-radius: 6px;
                    border: 1px solid #e9ecef;

                    .label {
                        font-weight: 600;
                        color: #333;
                    }

                    .value {
                        font-size: 16px;
                        font-weight: 700;
                        color: #e6a23c;
                    }
                }
            }

            .amount-section {
                background: #fff;
                border-radius: 6px;
                padding: 10px;
                margin: 15px 0;
                border: 1px solid #e9ecef;
            }
        }
    }
}

.dialog-footer {
    text-align: center;

    .el-button {
        min-width: 100px;
    }
}
</style>
