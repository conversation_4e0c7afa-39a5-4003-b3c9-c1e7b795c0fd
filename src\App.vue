<template>
    <div id="app">
        <router-view />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Action } from 'vuex-class'
import store from './store'

@Component
export default class App extends Vue {
    @Action('getConfig') getConfig!: () => void
    @Action('getComboAuth') getComboAuth!: () => void
    async created() {
        const data: any = await this.getConfig()
        await this.getComboAuth()
        console.log(store.getters.comboAuth, 11111)

        let favicon: any = document.querySelector('link[rel="icon"]')!
        if (favicon) {
            favicon.href = data.favicon
            return
        }
        favicon = document.createElement('link')
        favicon.rel = 'icon'
        favicon.href = data.favicon
        document.head.appendChild(favicon)
    }
}
</script>

<style lang="scss">
@import './assets/fonts/iconfont.css';
@import './styles/index.scss';
#app {
    min-width: 1180px;
}
body {
    padding-right: 0px !important;
}
</style>
