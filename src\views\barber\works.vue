<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
  apiBarberList,
  apiBarberProjectList,
  apiBarberWorksList,
  apiBarberWorksSave,
  apiBarberWorksDel,
  apiBarberWorksDetail,
  apiBarberWorksCommentList,
  apiBarberWorksCommentDel
} from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'
import MaterialSelect from '@/components/material-select/index.vue'

@Component({
  name: 'BarberWorks',
  components: {
    LsPagination,
    MaterialSelect
  }
})
export default class BarberWorks extends Vue {
  // 当前选中的理发师
  private currentBarber = {
    id: '',
    name: ''
  }
  
  // 理发师列表
  private barberList: any[] = []
  
  // 作品列表
  private worksList: any[] = []
  
  // 理发师项目列表
  private projectList: any[] = []
  
  // 分页数据
  private pager = new RequestPaging()
  
  // 作品弹窗
  private worksDialog = {
    visible: false,
    title: '新增作品',
    isEdit: false
  }
  
  // 作品表单
  private worksForm = {
    id: '',
    barber_id: '',
    title: '',
    images: '' as string | string[], // 修改类型定义，允许字符串或字符串数组
    content: '',
    barber_service_id: '', // 理发师项目ID
    image: '' // 封面图片
  }
  
  // 作品表单规则
  private worksRules = {
    title: [{ required: true, message: '请输入作品标题', trigger: 'blur' }],
    images: [{ required: true, message: '请上传作品图片', trigger: 'change' }],
    content: [{ required: true, message: '请输入作品描述', trigger: 'blur' }],
    barber_service_id: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
    image: [{ required: true, message: '请上传封面图片', trigger: 'change' }]
  }
  
  // 搜索理发师关键词
  private barberSearchKeyword = ''
  
  // 搜索作品关键词
  private worksSearchKeyword = ''
  
  // 加载状态
  private loading = false
  
  // 图片查看器
  private imageViewer = {
    visible: false,
    images: [] as string[],
    index: 0
  }
  
  // 评论弹窗
  private commentDialog = {
    visible: false,
    worksId: '',
    worksTitle: ''
  }
  
  // 评论列表
  private commentList: any[] = []
  
  // 评论分页
  private commentPager = new RequestPaging()
  
  // 加载评论状态
  private commentLoading = false
  
  created() {
    this.getBarberList()
  }
  
  // 获取理发师列表
  async getBarberList() {
    try {
      this.loading = true
      const res = await apiBarberList({
        page_no: 1,
        page_size: 10000
      })
      this.barberList = res.lists || []
      if (this.barberList.length > 0) {
        // 默认选中第一个理发师
        this.handleSelectBarber(this.barberList[0])
      }
    } catch (error) {
      console.error('获取理发师列表失败', error)
    } finally {
      this.loading = false
    }
  }
  
  // 选择理发师
  handleSelectBarber(barber: any) {
    this.currentBarber = {
      id: barber.id,
      name: barber.name
    }
    this.getWorksList()
    this.getBarberProjectList()
  }
  
  // 获取理发师作品列表
  async getWorksList() {
    if (!this.currentBarber.id) {
return
}
    
    try {
      this.loading = true
      this.pager.page = 1
      await this.pager.request({
        callback: apiBarberWorksList,
        params: {
          barber_id: this.currentBarber.id,
          title: this.worksSearchKeyword
        }
      })
    } catch (error) {
      console.error('获取理发师作品列表失败', error)
    } finally {
      this.loading = false
    }
  }
  
  // 获取理发师项目列表
  async getBarberProjectList() {
    if (!this.currentBarber.id) {
return
}
    
    try {
      const res = await apiBarberProjectList({ id: this.currentBarber.id })
      this.projectList = res.lists || []
    } catch (error) {
      console.error('获取理发师项目列表失败', error)
      this.projectList = []
    }
  }
  
  // 打开新增作品弹窗
  handleAddWorks() {
    this.worksDialog = {
      visible: true,
      title: '新增作品',
      isEdit: false
    }
    this.worksForm = {
      id: '',
      barber_id: this.currentBarber.id,
      title: '',
      images: '',
      content: '',
      barber_service_id: '',
      image: ''
    }
  }
  
  // 打开编辑作品弹窗
  async handleEditWorks(works: any) {
    this.worksDialog = {
      visible: true,
      title: '编辑作品',
      isEdit: true
    }
    
    try {
      this.loading = true
      const res = await apiBarberWorksDetail({ id: works.id })
      if (res) {
        // 处理图片集数据，确保它是字符串或数组格式
        let imagesArray: string[] = []
        if (res.images) {
          // 如果images是字符串，则拆分为数组
          if (typeof res.images === 'string') {
            imagesArray = res.images.split(',')
          } else if (Array.isArray(res.images)) {
            imagesArray = res.images
          }
        }
        
        // 将获取到的数据填充到表单中
        this.worksForm = {
          id: res.id,
          barber_id: this.currentBarber.id,
          title: res.title,
          images: imagesArray,
          content: res.content,
          barber_service_id: res.barber_service_id,
          image: res.image || ''
        }
        
        console.log('加载的作品详情：', this.worksForm)
      }
    } catch (error) {
      console.error('获取作品详情失败', error)
    } finally {
      this.loading = false
    }
  }
  
  // 删除作品
  async handleDeleteWorks(works: any) {
    try {
      await this.$confirm('确认删除该作品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      this.loading = true
      try {
        await apiBarberWorksDel({ id: works.id })
        this.$message.success('删除成功')
        this.getWorksList()
      } catch (error) {
        console.error('删除作品失败', error)
        this.$message.error('删除失败')
      }
    } catch (error) {
      console.log('取消删除')
    } finally {
      this.loading = false
    }
  }
  
  // 保存作品
  async handleSaveWorks() {
    const formRef = this.$refs.worksForm as any
    if (!formRef) {
return
}
    
    try {
      await formRef.validate()
      
      this.loading = true
      try {
        // 准备要提交的数据
        const params = {
          ...this.worksForm
        }
        
        // 如果images是数组，将其转换为用英文逗号分隔的字符串
        if (Array.isArray(params.images)) {
          params.images = params.images.join(',')
        }
        
        // 调用保存API
        await apiBarberWorksSave(params)
        this.$message.success(this.worksDialog.isEdit ? '修改成功' : '新增成功')
        this.worksDialog.visible = false
        this.getWorksList()
      } catch (error) {
        console.error('保存作品失败', error)
        this.$message.error(this.worksDialog.isEdit ? '修改失败' : '新增失败')
      }
    } catch (error) {
      console.error('表单验证失败', error)
    } finally {
      this.loading = false
    }
  }
  
  // 关闭弹窗
  handleCloseDialog() {
    this.worksDialog.visible = false
  }
  
  // 处理理发师搜索
  handleBarberSearch() {
    if (!this.barberSearchKeyword) {
return
}
    
    const filtered = this.barberList.filter(item => 
      item.name.includes(this.barberSearchKeyword) || 
      (item.mobile && item.mobile.includes(this.barberSearchKeyword))
    )
    
    if (filtered.length > 0) {
      this.handleSelectBarber(filtered[0])
    }
  }
  
  // 处理作品搜索
  handleWorksSearch() {
    this.getWorksList()
  }
  
  // 格式化图片列表
  formatImageList(images: string): string[] {
    if (!images) {
return []
}
    return images.split(',')
  }
  
  // 辅助方法：判断字符串是否为URL
  isValidUrl(str: string): boolean {
    if (!str) {
return false
}
    return str.startsWith('http://') || str.startsWith('https://')
  }
  
  // 预览图片
  previewImage(images: string, index = 0) {
    const imageList = this.formatImageList(images)
    if (imageList.length > 0) {
      this.imageViewer = {
        visible: true,
        images: imageList,
        index: index
      }
    }
  }
  
  // 获取项目名称
  getProjectName(projectId: string | number): string {
    const project = this.projectList.find(p => p.id == projectId)
    return project ? project.title : '-'
  }
  
  // 格式化时间
  formatDate(timestamp: number): string {
    if (!timestamp) {
return '-'
}
    const date = new Date(timestamp * 1000)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  
  // 打开评论弹窗
  async handleViewComments(works: any) {
    this.commentDialog = {
      visible: true,
      worksId: works.id,
      worksTitle: works.title
    }
    await this.getCommentList()
  }
  
  // 获取评论列表
  async getCommentList() {
    if (!this.commentDialog.worksId) {
return
}
    
    try {
      this.commentLoading = true
      this.commentPager.page = 1
      await this.commentPager.request({
        callback: apiBarberWorksCommentList,
        params: {
          id: this.commentDialog.worksId
        }
      })
    } catch (error) {
      console.error('获取评论列表失败', error)
    } finally {
      this.commentLoading = false
    }
  }
  
  // 删除评论
  async handleDeleteComment(comment: any) {
    try {
      await this.$confirm('确认删除该评论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      this.commentLoading = true
      try {
        await apiBarberWorksCommentDel({ id: comment.id })
        this.$message.success('删除成功')
        this.getCommentList()
      } catch (error) {
        console.error('删除评论失败', error)
        this.$message.error('删除失败')
      }
    } catch (error) {
      console.log('取消删除')
    } finally {
      this.commentLoading = false
    }
  }
}
</script>

<template>
  <div class="barber-works-container">
    <!-- 页面标题 -->
    <div class="page-title">理发师作品管理</div>
    
    <div class="barber-works-content">
      <!-- 左侧理发师列表 -->
      <div class="barber-list-panel">
        <div class="panel-title">理发师列表</div>
        <el-input 
          v-model="barberSearchKeyword" 
          placeholder="搜索理发师" 
          prefix-icon="el-icon-search"
          class="barber-search" 
          clearable 
          @keyup.enter.native="handleBarberSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleBarberSearch"></el-button>
        </el-input>
        <div class="barber-items">
          <div 
            v-for="item in barberList" 
            :key="item.id" 
            class="barber-item"
            :class="{ 'active': currentBarber.id === item.id }"
            @click="handleSelectBarber(item)"
          >
            <el-avatar :src="item.image" :size="36"></el-avatar>
            <div class="barber-name">{{ item.name }}</div>
          </div>
          <div v-if="barberList.length === 0" class="empty-tip">
            暂无理发师
          </div>
        </div>
      </div>
      
      <!-- 右侧作品列表 -->
      <div class="works-panel">
        <div class="panel-header">
          <div class="panel-title">
            {{ currentBarber.name ? `${currentBarber.name}的作品` : '请选择理发师' }}
          </div>
          <div class="panel-actions">
            <el-input 
              v-model="worksSearchKeyword" 
              placeholder="搜索作品标题" 
              clearable
              size="small"
              style="width: 200px; margin-right: 10px;"
              @keyup.enter.native="handleWorksSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleWorksSearch"></el-button>
            </el-input>
            <el-button 
              type="primary" 
              size="small" 
              icon="el-icon-plus"
              :disabled="!currentBarber.id || !projectList.length"
              @click="handleAddWorks"
            >
              新增作品
            </el-button>
          </div>
        </div>
        
        <!-- 作品卡片列表 -->
        <div class="works-list" v-if="currentBarber.id && pager.lists.length > 0">
          <el-card 
            v-for="item in pager.lists" 
            :key="item.id" 
            class="works-card"
            shadow="hover"
          >
            <div class="works-cover" @click="previewImage(item.images)">
              <el-image 
                :src="item.image" 
                fit="cover"
              >
                <div slot="error" class="image-error">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div class="works-images-count" v-if="formatImageList(item.images).length > 1">
                <i class="el-icon-picture"></i> {{ formatImageList(item.images).length }}
              </div>
            </div>
            <div class="works-info">
              <h3 class="works-title">{{ item.title }}</h3>
              <div class="works-project">
                <span class="label">关联项目:</span>
                <span class="value">{{ getProjectName(item.barber_service_id) }}</span>
              </div>
              <div class="works-time">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(item.create_time) }}</span>
              </div>
              <div class="works-content">{{ item.content }}</div>
              <div class="works-actions">
                <el-button type="text" size="small" @click="handleEditWorks(item)">编辑</el-button>
                <el-button type="text" size="small" @click="handleViewComments(item)">评论</el-button>
                <el-button type="text" size="small" class="delete-btn" @click="handleDeleteWorks(item)">删除</el-button>
              </div>
            </div>
          </el-card>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container" v-if="currentBarber.id && pager.lists.length > 0">
          <ls-pagination v-model="pager" @change="getWorksList()" />
        </div>
        
        <!-- 未选择理发师或无作品时的提示 -->
        <div class="empty-works" v-if="currentBarber.id && pager.lists.length === 0">
          <el-empty description="暂无作品"></el-empty>
        </div>
        <div class="empty-works" v-if="!currentBarber.id">
          <el-empty description="请先选择理发师"></el-empty>
        </div>
      </div>
    </div>
    
    <!-- 作品表单弹窗 -->
    <el-dialog
      :title="worksDialog.title"
      :visible.sync="worksDialog.visible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleCloseDialog"
    >
      <el-form 
        ref="worksForm" 
        :model="worksForm" 
        :rules="worksRules" 
        label-width="100px"
        size="small"
      >
        <el-form-item label="作品标题" prop="title">
          <el-input v-model="worksForm.title" placeholder="请输入作品标题"></el-input>
        </el-form-item>
        
        <el-form-item label="关联项目" prop="barber_service_id">
          <el-select v-model="worksForm.barber_service_id" placeholder="请选择关联项目" style="width: 100%">
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="封面图片" prop="image">
          <material-select :limit="1" v-model="worksForm.image" />
          <div class="upload-tip">请上传作品封面图片</div>
        </el-form-item>
        
        <el-form-item label="作品图片" prop="images">
          <material-select v-model="worksForm.images" :limit="9" multiple />
          <div class="upload-tip">最多可上传9张图片，多张图片请用英文逗号分隔</div>
        </el-form-item>
        
        <el-form-item label="作品描述" prop="content">
          <el-input 
            v-model="worksForm.content" 
            type="textarea" 
            :rows="4"
            placeholder="请输入作品描述"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="worksDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveWorks" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 图片查看器 -->
    <el-dialog :visible.sync="imageViewer.visible" append-to-body width="800px">
      <div class="image-viewer-container">
        <el-carousel 
          :initial-index="imageViewer.index"
          height="500px"
          indicator-position="outside"
          arrow="always"
        >
          <el-carousel-item v-for="(img, index) in imageViewer.images" :key="index">
            <div class="image-container">
              <el-image 
                :src="img" 
                fit="cover"
                style="height: 100%; width: 100%;"
              ></el-image>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </el-dialog>
    
    <!-- 评论弹窗 -->
    <el-dialog
      title="作品评论"
      :visible.sync="commentDialog.visible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="comment-header">
        <h3>{{ commentDialog.worksTitle }}</h3>
      </div>
      
      <div class="comment-list" v-loading="commentLoading">
        <div v-if="commentPager.lists.length === 0" class="empty-comment">
          <el-empty description="暂无评论"></el-empty>
        </div>
        
        <div v-else class="comment-items">
          <div v-for="item in commentPager.lists" :key="item.id" class="comment-item">
            <div class="comment-user">
              <el-avatar :src="item.avatar" :size="32"></el-avatar>
              <span class="username">{{ item.nickname }}</span>
            </div>
            <div class="comment-content">{{ item.content }}</div>
            <div class="comment-footer">
              <span class="comment-time">{{ item.create_time ? formatDate(item.create_time) : '-' }}</span>
              <el-button 
                type="text" 
                size="mini" 
                class="delete-btn"
                @click="handleDeleteComment(item)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 评论分页 -->
        <div class="comment-pagination" v-if="commentPager.lists.length > 0">
          <ls-pagination v-model="commentPager" @change="getCommentList()" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.barber-works-container {
  padding: 20px;
  
  .page-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
  }
  
  .barber-works-content {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    
    .barber-list-panel {
      width: 250px;
      border-right: 1px solid #ebeef5;
      padding: 15px;
      
      .panel-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
      }
      
      .barber-search {
        margin-bottom: 15px;
      }
      
      .barber-items {
        max-height: 600px;
        overflow-y: auto;
        
        .barber-item {
          display: flex;
          align-items: center;
          padding: 10px;
          cursor: pointer;
          border-radius: 4px;
          margin-bottom: 5px;
          
          &:hover {
            background-color: #f5f7fa;
          }
          
          &.active {
            background-color: #ecf5ff;
          }
          
          .barber-name {
            margin-left: 10px;
            font-size: 14px;
          }
        }
        
        .empty-tip {
          text-align: center;
          color: #909399;
          padding: 20px 0;
        }
      }
    }
    
    .works-panel {
      flex: 1;
      padding: 15px;
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .panel-title {
          font-size: 16px;
          font-weight: bold;
        }
      }
      
      .works-list {
        display: flex;
        flex-wrap: wrap;
        
        .works-card {
          width: calc(33.33% - 20px);
          margin: 0 10px 20px;
          
          .works-cover {
            height: 180px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
            
            .el-image {
              width: 100%;
              height: 100%;
            }
            
            .image-error {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
              background-color: #f5f7fa;
              color: #909399;
              font-size: 30px;
            }
            
            .works-images-count {
              position: absolute;
              bottom: 10px;
              right: 10px;
              background-color: rgba(0,0,0,0.6);
              color: #fff;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
            }
          }
          
          .works-info {
            padding: 10px 0;
            
            .works-title {
              margin: 0 0 10px;
              font-size: 16px;
              color: #303133;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .works-project, .works-time {
              margin-bottom: 5px;
              font-size: 12px;
              
              .label {
                color: #909399;
                margin-right: 5px;
              }
              
              .value {
                color: #606266;
              }
            }
            
            .works-content {
              margin: 10px 0;
              color: #606266;
              font-size: 12px;
              line-height: 1.6;
              height: 40px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            
            .works-actions {
              display: flex;
              justify-content: flex-end;
              margin-top: 10px;
            }
          }
        }
      }
      
      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }
      
      .empty-works {
        margin-top: 50px;
      }
    }
  }
  
  .delete-btn {
    color: #F56C6C;
  }
  
  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
  
  .image-viewer-container {
    .image-container {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  .comment-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }
  
  .comment-list {
    min-height: 200px;
    max-height: 400px;
    
    .empty-comment {
      padding: 40px 0;
    }
    
    .comment-items {
      height: 300px;
      overflow-y: auto;
      
      .comment-item {
        padding: 15px;
        border-bottom: 1px solid #ebeef5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .comment-user {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          .username {
            margin-left: 10px;
            font-size: 14px;
            color: #303133;
          }
        }
        
        .comment-content {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          margin-bottom: 10px;
        }
        
        .comment-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .comment-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
    
    .comment-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 