import Main from '@/layout/main.vue'
import Blank from '@/layout/blank.vue'
const routes = [
    {
        path: '/barber',
        name: 'barber',
        meta: {title: '理发师',},
        redirect: '/barber/list',
        component: Main,
        children: [
            {
                path: '/barber/list',
                name: 'barber_list',
                meta: {
                    title: '员工',
                    parentPath: '/barber',
                    icon: 'icon_order_guanli',
                    permission: ['view'],
                    keepAlive: true
                },
                component: () => import('@/views/barber/list.vue')
            },
            {
                path: '/barber/list_detail',
                name: 'barber_list_detail',
                meta: {
                    hidden: true,
                    title: '理发师详情',
                    parentPath: '/barber',
                    prevPath: '/barber/list'
                },
                component: () => import('@/views/barber/lists_detail.vue')
            },
            {
                path: '/barber/project',
                name: 'barber_project',
                meta: {
                    title: '理发师项目',
                    parentPath: '/barber',
                    icon: 'icon_order_guanli',
                    permission: ['view']
                },
                component: () => import('@/views/barber/project.vue')
            },
            {
                path: '/barber/works',
                name: 'barberWorks',
                component: () => import('@/views/barber/works.vue'),
                meta: {
                    title: '理发师作品',
                    parentPath: '/barber',
                    icon: 'icon_order_guanli',
                    permission: ['view']
                }
            },

            {
                path: '/barber/create_project',
                name: 'barberCreateProject',
                meta: {
                    title: '项目列表',
                    parentPath: '/barber',
                    icon: 'icon_order_guanli',
                    permission: ['view']
                },
                component: () => import('@/views/barber/list_project.vue')
            },
            {
                path: '/barber/job',
                name: 'barberJob',
                meta: {
                    title: '职位管理',
                    parentPath: '/barber',
                    icon: 'icon_order_guanli',
                    permission: ['view']
                },
                component: () => import('@/views/barber/job.vue')
            }
        ]
    }
]

export default routes
