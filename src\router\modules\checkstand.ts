import Main from '@/layout/main.vue'
import Blank from '@/layout/blank.vue'


const routes = [
    {
        path: '/checkstand',
        name: 'checkstand',
        meta: {title: '收银台',},
        redirect: '/checkstand/list',
        component: Main,
        children: [
            {
                path: '/checkstand/list',
                name: 'checkstand_list',
                meta: {
                    title: '收银',
                    parentPath: '/checkstand',
                    icon: 'icon_order_guanli',
                    permission: ['view'],
                    keepAlive: true
                },
                component: () => import('@/views/checkstand/cashier.vue')
            },
            {
                path: '/checkstand/appointment',
                name: 'checkstandAppointment',
                component: () => import('@/views/barber/appointment.vue'),
                meta: {
                    title: '理发师预约',
                    parentPath: '/checkstand',
                    icon: 'icon_order_guanli',
                    permission: ['view']
                }
            },
        ]
    }
]

export default routes
