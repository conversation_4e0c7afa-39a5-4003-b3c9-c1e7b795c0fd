<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiStoreSave, apiStoreUpdate, apiStoreDetail } from '@/api/barber/barber'
import MaterialSelect from '@/components/material-select/index.vue'

@Component({
    name: 'StoreDetail',
    components: {
        MaterialSelect
    }
})
export default class StoreDetail extends Vue {
    // 是否为编辑模式
    private isEdit = false

    // 店铺详情表单
    private storeForm = {
        id: '',
        name: '十七',
        address: '河北省',
        province: '130000',
        city: '130100',
        district: '130102',
        latitude: '38.033812',
        longitude: '114.514869',
        business_start_time: '09:00',
        business_end_time: '',
        weekdays: [] as string[],
        contact: '张三',
        mobile: '***********',
        image: '',
        images: '',
        remark: '123123',
        status: '1',
        account: '', // 店长登录账号
        password: '' // 店长登录密码
    }

    // 表单验证规则
    private rules = {
        name: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入店铺地址', trigger: 'blur' }],
        province: [
            { required: true, message: '请输入省份编码', trigger: 'blur' },
            { pattern: /^\d+$/, message: '省份必须是数字', trigger: 'blur' }
        ],
        city: [
            { required: true, message: '请输入城市编码', trigger: 'blur' },
            { pattern: /^\d+$/, message: '城市必须是数字', trigger: 'blur' }
        ],
        district: [
            { required: true, message: '请输入区/县编码', trigger: 'blur' },
            { pattern: /^\d+$/, message: '区/县必须是数字', trigger: 'blur' }
        ],
        contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        mobile: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        business_start_time: [{ required: true, message: '请选择营业开始时间', trigger: 'change' }],
        business_end_time: [{ required: true, message: '请选择营业结束时间', trigger: 'change' }],
        weekdays: [{ required: true, message: '请选择营业日', trigger: 'change' }],
        image: [{ required: true, message: '请上传店铺图片', trigger: 'change' }],
        account: [
            { required: true, message: '请输入店长账号', trigger: 'blur' },
            { min: 3, max: 20, message: '账号长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
            { required: true, message: '请输入店长密码', trigger: 'blur' },
            { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ]
    }

    // 营业日选项
    private weekdaysOptions = [
        { label: '周一', value: '1' },
        { label: '周二', value: '2' },
        { label: '周三', value: '3' },
        { label: '周四', value: '4' },
        { label: '周五', value: '5' },
        { label: '周六', value: '6' },
        { label: '周日', value: '7' }
    ]

    // 店铺状态选项
    private statusOptions = [
        { label: '正常营业', value: '1' },
        { label: '暂停营业', value: '0' }
    ]

    // 加载状态
    private loading = false

    created() {
        // 判断是否为编辑模式
        const id = this.$route.query.id
        if (id) {
            this.isEdit = true
            this.getStoreDetail(id as string)
        }
    }

    // 获取店铺详情
    async getStoreDetail(id: string) {
        this.loading = true
        try {
            // 直接获取接口返回的data数据，因为axios拦截器已经处理了响应
            const data = await apiStoreDetail({ id })
            console.log('接口返回的data数据:', data)

            // 处理营业日数据，确保是数组格式
            let weekdays: string[] = []
            if (data.weekdays) {
                if (typeof data.weekdays === 'string') {
                    // 将"1,2,3,4,5,6"格式转换为["1","2","3","4","5","6"]数组
                    weekdays = data.weekdays.split(',').filter(Boolean)
                } else if (Array.isArray(data.weekdays)) {
                    weekdays = data.weekdays.map((item: any) => String(item))
                }
            }

            // 处理时间格式 - 处理完整日期格式
            let business_start_time = ''
            let business_end_time = ''

            // 如果有营业开始时间，从日期中提取时间
            if (data.business_start_time) {
                const date = new Date(data.business_start_time)
                if (!isNaN(date.getTime())) {
                    // 如果是有效日期，则提取时间部分
                    business_start_time = `${String(date.getHours()).padStart(2, '0')}:${String(
                        date.getMinutes()
                    ).padStart(2, '0')}`
                } else {
                    // 如果不是有效日期，设置默认值
                    business_start_time = '09:00'
                }
            }

            // 如果有营业结束时间，从日期中提取时间
            if (data.business_end_time) {
                const date = new Date(data.business_end_time)
                if (!isNaN(date.getTime())) {
                    // 如果是有效日期，则提取时间部分
                    business_end_time = `${String(date.getHours()).padStart(2, '0')}:${String(
                        date.getMinutes()
                    ).padStart(2, '0')}`
                } else {
                    // 如果不是有效日期，设置默认值
                    business_end_time = '18:00'
                }
            }

            console.log('处理后的数据:', {
                weekdays,
                business_start_time,
                business_end_time,
                原始营业日: data.weekdays,
                原始开始时间: data.business_start_time,
                原始结束时间: data.business_end_time
            })

            // 确保数据类型正确
            const storeStatus = typeof data.status === 'number' ? String(data.status) : data.status

            // 填充表单数据
            this.storeForm = {
                id: data.id || '',
                name: data.name || '',
                address: data.address || '',
                province: data.province ? String(data.province) : '',
                city: data.city ? String(data.city) : '',
                district: data.district ? String(data.district) : '',
                latitude: data.latitude || '',
                longitude: data.longitude || '',
                business_start_time: business_start_time,
                business_end_time: business_end_time,
                weekdays: weekdays,
                contact: data.contact || '',
                mobile: data.mobile || '',
                image: data.image || '',
                images: data.images || '',
                remark: data.remark || '',
                status: storeStatus || '1',
                account: data.account || '', // 店长登录账号
                password: data.password || '' // 店长登录密码
            }

            console.log('处理后的表单数据:', this.storeForm)
        } catch (error) {
            console.error('获取店铺详情失败', error)
            this.$message.error('获取店铺详情失败: ' + (error instanceof Error ? error.message : '未知错误'))
        } finally {
            this.loading = false
        }
    }

    // 提交表单
    async submitForm() {
        const formRef = this.$refs.storeForm as any
        if (!formRef) {
            return
        }

        try {
            await formRef.validate()

            // 处理表单提交
            this.loading = true

            // 准备提交的数据
            const params: any = { ...this.storeForm }

            // 将weekdays数组转换为逗号分隔的字符串
            if (Array.isArray(params.weekdays)) {
                params.weekdays = params.weekdays.join(',')
            }

            // 确保时间格式正确
            if (!params.business_start_time) {
                params.business_start_time = '09:00'
            }

            if (!params.business_end_time) {
                params.business_end_time = '18:00'
            }

            // 添加调试信息，查看提交的参数
            console.log('准备提交的数据:', params)
            console.log('是否为编辑模式:', this.isEdit)
            console.log('路由参数id:', this.$route.query.id)

            try {
                // 根据是否为编辑模式调用不同的API
                if (this.isEdit) {
                    // 确保id被正确传递
                    if (!params.id) {
                        params.id = this.$route.query.id as string
                    }
                    console.log('最终提交的编辑数据:', params)
                    await apiStoreUpdate(params)
                    this.$message.success('修改成功')
                } else {
                    // 新增时确保不传id
                    delete params.id
                    console.log('最终提交的新增数据:', params)
                    await apiStoreSave(params)
                    this.$message.success('新增成功')
                }

                // 返回上一页
                this.$router.back()
            } catch (error: any) {
                console.error('保存店铺信息失败', error)
                this.$message.error('保存失败: ' + (error.response?.data?.msg || '未知错误'))
            }
        } catch (error) {
            console.error('表单验证失败', error)
        } finally {
            this.loading = false
        }
    }

    // 取消编辑，返回上一页
    goBack() {
        this.$router.back()
    }
}
</script>

<template>
    <div class="store-detail-container">
        <!-- 页面标题 -->
        <div class="page-title">{{ isEdit ? '编辑店铺' : '新增店铺' }}</div>

        <!-- 表单 -->
        <div class="form-container">
            <el-form
                ref="storeForm"
                :model="storeForm"
                :rules="rules"
                label-width="120px"
                size="small"
                v-loading="loading"
            >
                <el-form-item label="店铺名称" prop="name">
                    <el-input v-model="storeForm.name" placeholder="请输入店铺名称"></el-input>
                </el-form-item>

                <el-form-item label="省份编码" prop="province">
                    <el-input
                        v-model="storeForm.province"
                        placeholder="请输入省份编码(数字)"
                        oninput="value=value.replace(/[^\d]/g,'')"
                    ></el-input>
                    <div class="input-tip">省份必须是数字编码</div>
                </el-form-item>

                <el-form-item label="城市编码" prop="city">
                    <el-input
                        v-model="storeForm.city"
                        placeholder="请输入城市编码(数字)"
                        oninput="value=value.replace(/[^\d]/g,'')"
                    ></el-input>
                    <div class="input-tip">城市必须是数字编码</div>
                </el-form-item>

                <el-form-item label="区/县编码" prop="district">
                    <el-input
                        v-model="storeForm.district"
                        placeholder="请输入区/县编码(数字)"
                        oninput="value=value.replace(/[^\d]/g,'')"
                    ></el-input>
                    <div class="input-tip">区/县必须是数字编码</div>
                </el-form-item>

                <el-form-item label="详细地址" prop="address">
                    <el-input v-model="storeForm.address" placeholder="请输入详细地址"></el-input>
                </el-form-item>

                <el-form-item label="经纬度">
                    <div class="location-inputs">
                        <el-input
                            v-model="storeForm.latitude"
                            placeholder="纬度"
                            style="width: 200px; margin-right: 10px"
                        ></el-input>
                        <el-input v-model="storeForm.longitude" placeholder="经度" style="width: 200px"></el-input>
                    </div>
                    <div class="location-tip">选填项，用于在地图上定位您的店铺</div>
                </el-form-item>

                <el-form-item label="营业时间">
                    <div class="business-time">
                        <el-time-picker
                            v-model="storeForm.business_start_time"
                            format="HH:mm"
                            value-format="HH:mm"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            placeholder="开始时间"
                            style="width: 200px; margin-right: 10px"
                        ></el-time-picker>
                        <span style="margin-right: 10px">至</span>
                        <el-time-picker
                            v-model="storeForm.business_end_time"
                            format="HH:mm"
                            value-format="HH:mm"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            placeholder="结束时间"
                            style="width: 200px"
                        ></el-time-picker>
                    </div>
                    <div class="time-tip">请选择24小时制时间，如09:00表示上午9点</div>
                </el-form-item>

                <el-form-item label="营业日" prop="weekdays">
                    <el-checkbox-group v-model="storeForm.weekdays">
                        <el-checkbox v-for="item in weekdaysOptions" :key="item.value" :label="item.value"
                            >{{ item.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item label="联系人" prop="contact">
                    <el-input v-model="storeForm.contact" placeholder="请输入联系人姓名"></el-input>
                </el-form-item>

                <el-form-item label="联系电话" prop="mobile">
                    <el-input v-model="storeForm.mobile" placeholder="请输入联系电话"></el-input>
                </el-form-item>

                <el-form-item label="店长账号" prop="account">
                    <el-input v-model="storeForm.account" placeholder="请输入店长登录账号（全局唯一）"></el-input>
                    <div class="input-tip">店长登录账号，全局唯一，3-20个字符</div>
                </el-form-item>

                <el-form-item label="店长密码" prop="password">
                    <el-input
                        v-model="storeForm.password"
                        type="password"
                        placeholder="请输入店长登录密码（≥6位）"
                        show-password
                    ></el-input>
                    <div class="input-tip">店长登录密码，6-20个字符</div>
                </el-form-item>

                <el-form-item label="店铺状态" prop="status">
                    <el-radio-group v-model="storeForm.status">
                        <el-radio v-for="item in statusOptions" :key="item.value" :label="item.value"
                            >{{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="店铺封面" prop="image">
                    <material-select v-model="storeForm.image" :limit="1" />
                    <div class="upload-tip">请上传店铺封面图片</div>
                </el-form-item>

                <el-form-item label="店铺轮播图">
                    <material-select v-model="storeForm.images" :limit="5" multiple />
                    <div class="upload-tip">最多可上传5张图片，用于店铺轮播展示</div>
                </el-form-item>

                <el-form-item label="备注">
                    <el-input
                        v-model="storeForm.remark"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入备注信息"
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.store-detail-container {
    padding: 20px;

    .page-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .form-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 4px;

        .location-inputs {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .location-tip {
            font-size: 12px;
            color: #909399;
            margin-bottom: 10px;
        }

        .input-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }

        .business-time {
            display: flex;
            align-items: center;
        }

        .time-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }

        .upload-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
    }
}
</style>
