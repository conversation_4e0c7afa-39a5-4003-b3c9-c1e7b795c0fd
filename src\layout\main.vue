<template>
    <el-container class="ls-layout">
        <el-header class="bg-primary" :height="styleConfig.headerHeight">
            <ls-header />
        </el-header>
        <div style="padding: 10px 16px 0 16px" v-if="config.platform_shop.expires_show">
            <el-alert class="xxl" type="info" :closable="true" show-icon>
                <template slot="default">
                    <div class="flex" style="width: 100%">
                        <span style="color: #337ecc">
                            您的店铺服务套餐
                            <span style="color: #e6a23c">即将到期</span>

                            ，如需正常营业，请联系平台客服</span
                        >
                        <div style="margin-left: auto; display: flex">
                            <el-button type="danger" size="small" @click="handleGoRenew" style="margin-right: 5px"
                                >立即续费</el-button
                            >
                            <ls-dialog
                                width="550px"
                                top="20vh"
                                ref="dialog"
                                title="联系客服"
                                cancelButtonText=""
                                confirmButtonText=""
                            >
                                <template slot="trigger">
                                    <el-button type="primary" size="small"> 联系客服</el-button>
                                </template>
                                <template>
                                    <div
                                        style="
                                            display: flex;
                                            justify-content: center;
                                            flex-direction: column;
                                            align-items: center;
                                        "
                                    >
                                        <el-image
                                            style="width: 200px; height: 200px"
                                            fit="cover"
                                            :src="config.platform_service.service_qrcode"
                                        ></el-image>
                                        <div class="m-t-20">微信扫码联系平台客服</div>
                                        <div class="m-t-20">客服电话：{{ config.platform_service.service_mobile }}</div>
                                    </div>
                                </template>
                            </ls-dialog>
                        </div>
                    </div>
                </template>
            </el-alert>
        </div>

        <el-container class="ls-container">
            <el-aside :width="styleConfig.asideMenuWidth" class="bg-white" v-if="!hideAsideMenu">
                <ls-aside :menu="asideMenu" />
            </el-aside>
            <el-main class="ls-main">
                <layout />
            </el-main>
        </el-container>
    </el-container>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import LsHeader from '@/components/layout/header.vue'
import Layout from '@/layout/index.vue'
import LsAside from '@/components/layout/aside.vue'
import { asyncRoutes } from '@/router'
import LsDialog from '@/components/ls-dialog.vue'
import store from '@/store'

@Component({
    components: {
        LsHeader,
        Layout,
        LsAside,
        LsDialog
    }
})
export default class Aside extends Vue {
    get asideMenu() {
        const { meta } = this.$route
        let item = meta?.moduleName
            ? asyncRoutes.find(item => item.meta?.moduleName === meta?.moduleName)
            : asyncRoutes.find(item => item.path === meta?.parentPath)
        return item?.children
    }
    get hideAsideMenu() {
        return this.asideMenu?.every((item: any) => item.meta?.hidden)
    }
    handleGoRenew() {
        this.$router.push('/setting/shop/renew')
    }
    get config() {
        return this.$store.getters.config
    }
}
</script>

<style scoped lang="scss">
.el-aside {
    height: calc(100vh - #{$--header-height});
}
::v-deep .el-alert__content {
    width: 100%;
    .el-alert__description {
        margin: 0px 10px 0 0;
    }
}
</style>
