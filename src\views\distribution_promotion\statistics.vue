<template>
    <div class="distribution-statistics" v-loading="loading">
        <!-- 总体统计 -->
        <div class="total-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>总体数据</span>
                </div>
                <el-row :gutter="20" v-if="statistics.total">
                    <el-col :span="6">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.total.users }}</div>
                            <div class="stat-label">分销用户数</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.total.active_users }}</div>
                            <div class="stat-label">活跃用户数</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.total.invites }}</div>
                            <div class="stat-label">总邀请数</div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.total.commission }}</div>
                            <div class="stat-label">总佣金</div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </div>

        <!-- 今日数据 -->
        <div class="today-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>今日数据</span>
                </div>
                <el-row :gutter="20" v-if="statistics.today">
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.today.users }}</div>
                            <div class="stat-label">新增用户</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.today.invites }}</div>
                            <div class="stat-label">新增邀请</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.today.commission }}</div>
                            <div class="stat-label">今日佣金</div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </div>

        <!-- 本月数据 -->
        <div class="month-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>本月数据</span>
                </div>
                <el-row :gutter="20" v-if="statistics.month">
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.month.users }}</div>
                            <div class="stat-label">新增用户</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">{{ statistics.month.invites }}</div>
                            <div class="stat-label">新增邀请</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.month.commission }}</div>
                            <div class="stat-label">本月佣金</div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </div>

        <!-- 提现数据 -->
        <div class="withdraw-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>提现数据</span>
                </div>
                <el-row :gutter="20" v-if="statistics.withdraw">
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.withdraw.total }}</div>
                            <div class="stat-label">总提现</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.withdraw.pending }}</div>
                            <div class="stat-label">待审核</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stat-item">
                            <div class="stat-value">¥{{ statistics.withdraw.success }}</div>
                            <div class="stat-label">成功提现</div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
        </div>

        <!-- 等级分布统计 -->
        <div class="level-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>等级分布统计</span>
                </div>
                <el-table
                    :data="levelStatistics"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column prop="level_name" label="等级名称" width="150" />
                    <el-table-column prop="user_count" label="用户数量" width="100" />
                    <el-table-column prop="min_invite" label="最少邀请" width="100" />
                    <el-table-column prop="max_invite" label="最多邀请" width="100" />
                    <el-table-column prop="first_rate" label="一级佣金率" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.first_rate }}%
                        </template>
                    </el-table-column>
                    <el-table-column prop="second_rate" label="二级佣金率" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.second_rate }}%
                        </template>
                    </el-table-column>
                    <el-table-column prop="invite_reward" label="邀请奖励" width="120">
                        <template slot-scope="scope">
                            ¥{{ scope.row.invite_reward }}
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>

        <!-- 提现统计详情 -->
        <div class="withdraw-detail-stats">
            <el-card>
                <div slot="header" class="card-header">
                    <span>提现统计详情</span>
                </div>
                <div class="withdraw-detail-content" v-if="withdrawStatistics">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ withdrawStatistics.total.amount }}</div>
                                <div class="stat-label">总提现金额</div>
                                <div class="stat-sub">{{ withdrawStatistics.total.count }}笔</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ withdrawStatistics.pending.amount }}</div>
                                <div class="stat-label">待审核金额</div>
                                <div class="stat-sub">{{ withdrawStatistics.pending.count }}笔</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ withdrawStatistics.success.amount }}</div>
                                <div class="stat-label">成功提现金额</div>
                                <div class="stat-sub">{{ withdrawStatistics.success.count }}笔</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="stat-item">
                                <div class="stat-value">¥{{ withdrawStatistics.reject.amount }}</div>
                                <div class="stat-label">拒绝提现金额</div>
                                <div class="stat-sub">{{ withdrawStatistics.reject.count }}笔</div>
                            </div>
                        </el-col>
                    </el-row>

                    <!-- 提现方式统计 -->
                    <div class="account-type-stats" v-if="withdrawStatistics.account_type_stats">
                        <h4>提现方式统计</h4>
                        <el-table
                            :data="withdrawStatistics.account_type_stats"
                            border
                            stripe
                            style="width: 100%"
                        >
                            <el-table-column prop="type_name" label="提现方式" width="150" />
                            <el-table-column prop="amount" label="提现金额" width="150">
                                <template slot-scope="scope">
                                    ¥{{ scope.row.amount }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="count" label="提现笔数" width="100" />
                        </el-table>
                    </div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { 
    apiDistributionPromotionStatistics,
    apiDistributionPromotionLevelStatistics,
    apiDistributionWithdrawStatistics
} from '@/api/distribution_promotion/distribution_promotion'
import * as Interface from '@/api/distribution_promotion/distribution_promotion.d'

@Component({
    name: 'DistributionStatistics'
})
export default class DistributionStatistics extends Vue {
    // 分销统计数据
    private statistics: Interface.DistributionStatistics = {} as Interface.DistributionStatistics

    // 等级统计数据
    private levelStatistics: Interface.LevelStatistics[] = []

    // 提现统计数据
    private withdrawStatistics: Interface.WithdrawStatistics = {} as Interface.WithdrawStatistics

    // 加载状态
    private loading = false

    created() {
        this.loadAllStatistics()
    }

    // 加载所有统计数据
    async loadAllStatistics() {
        this.loading = true
        try {
            await Promise.all([
                this.getDistributionStatistics(),
                this.getLevelStatistics(),
                this.getWithdrawStatistics()
            ])
        } catch (error) {
            console.error('加载统计数据失败', error)
            this.$message.error('加载统计数据失败')
        } finally {
            this.loading = false
        }
    }

    // 获取分销统计
    async getDistributionStatistics() {
        try {
            const res = await apiDistributionPromotionStatistics()
            if (res) {
                this.statistics = res
            }
        } catch (error) {
            console.error('获取分销统计失败', error)
        }
    }

    // 获取等级统计
    async getLevelStatistics() {
        try {
            const res = await apiDistributionPromotionLevelStatistics()
            if (res && Array.isArray(res)) {
                this.levelStatistics = res
            }
        } catch (error) {
            console.error('获取等级统计失败', error)
        }
    }

    // 获取提现统计
    async getWithdrawStatistics() {
        try {
            const res = await apiDistributionWithdrawStatistics()
            if (res) {
                this.withdrawStatistics = res
            }
        } catch (error) {
            console.error('获取提现统计失败', error)
        }
    }
}
</script>

<style lang="scss" scoped>
.distribution-statistics {
    padding: 20px;

    .total-stats,
    .today-stats,
    .month-stats,
    .withdraw-stats,
    .level-stats,
    .withdraw-detail-stats {
        margin-bottom: 20px;
    }

    .card-header {
        font-weight: bold;
        font-size: 16px;
    }

    .stat-item {
        text-align: center;
        padding: 20px 0;

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .stat-sub {
            color: #999;
            font-size: 12px;
        }
    }

    .withdraw-detail-content {
        .account-type-stats {
            margin-top: 30px;

            h4 {
                margin-bottom: 15px;
                color: #333;
            }
        }
    }
}

@media (max-width: 768px) {
    .distribution-statistics {
        .el-col {
            margin-bottom: 20px;
        }
    }
}
</style>
