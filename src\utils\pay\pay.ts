export enum PayWayEnum {
    WechatPay = 2,
    Alipay = 3,
    Transfer = 5
}

export class Pay {
    [x: string]: any
    // 维护模块名单
    modules = new Map()
    inject(name: string, module: any) {
        if (!name) {
            return this
        }
        this.modules.set(name, module)
        return this
    }
    getPayModules(payWay: PayWayEnum) {
        payWay = Number(payWay)
        const payWayStr = PayWayEnum[payWay]
        const module = this.modules.get(payWayStr)
        if (!module) {
            throw Error('未知的支付方式')
        }
        return module
    }
}
