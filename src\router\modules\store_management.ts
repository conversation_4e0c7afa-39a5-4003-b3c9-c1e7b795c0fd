import Main from '@/layout/main.vue'
import Blank from '@/layout/blank.vue'


const routes = [
  {
    path: '/store',
    name: 'store',
    meta: {title: '店铺管理',},
    redirect: '/store/store',
    component: Main,
    children: [
        {
            path: '/store/store',
            name: 'store_list',
            meta: {
                title: '店铺列表',
                parentPath: '/store',
                icon: 'icon_order_guanli',
                permission: ['view']
            },
            component: () => import('@/views/barber/store.vue')
        },
        {
            path: '/store/store_detail',
            name: 'store_detail',
            meta: {
                hidden: true,
                title: '店铺详情',
                parentPath: '/store',
                prevPath: '/store/store'
            },
            component: () => import('@/views/barber/store_detail.vue')
        },
    ],
  },
]

export default routes
