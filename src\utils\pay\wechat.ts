import { MessageBox } from 'element-ui'
import VueQr from 'vue-qr'
export default class WechatPay {
    pay(options: any, h: any) {
        return new Promise((resolve, reject) => {
            this.showQrCode(options, h).catch(error => {
                reject(error)
            })
        })
    }
    async showQrCode(options: any, h: any) {
        const qrCode = h(VueQr, {
            props: {
                text: options.code_url,
                size: 160,
                margin: 0
            }
        })
        const money = h('div', { class: 'primary xxl m-t-10' }, `¥${options.order_amount}`)
        const tips1 = h('div', { class: 'muted m-t-10' }, '请使用微信扫一扫')
        const tips2 = h('div', { class: 'muted m-t-6' }, '扫描支付')
        return MessageBox({
            title: '微信支付',
            showConfirmButton: false,
            closeOnClickModal: false,
            center: true,
            message: h('div', [qrCode, money, tips1, tips2])
        })
    }
}
