import request from '@/plugins/axios'

/** 分销用户管理 **/

// 分销用户列表
export const apiDistributionPromotionUserList = (params: any) => 
    request.get('/distribution_promotion/user_list', { params })

// 分销用户详情
export const apiDistributionPromotionUserDetail = (params: any) => 
    request.get('/distribution_promotion/user_detail', { params })

// 分销统计概览
export const apiDistributionPromotionStatistics = () => 
    request.get('/distribution_promotion/statistics')

// 等级分布统计
export const apiDistributionPromotionLevelStatistics = () => 
    request.get('/distribution_promotion/level_statistics')

// 禁用/启用分销用户
export const apiDistributionPromotionChangeStatus = (data: any) => 
    request.post('/distribution_promotion/change_status', data)

/** 提现管理 **/

// 提现申请列表
export const apiDistributionWithdrawList = (params: any) => 
    request.get('/distribution_withdraw/list', { params })

// 审核提现申请
export const apiDistributionWithdrawAudit = (data: any) => 
    request.post('/distribution_withdraw/audit', data)

// 提现统计
export const apiDistributionWithdrawStatistics = () => 
    request.get('/distribution_withdraw/statistics')
