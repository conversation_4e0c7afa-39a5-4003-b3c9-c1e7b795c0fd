<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiBarberSave, apiBarberDetail, apiSubShopList, apiBarberProjectList, apiBarberProjectSave, apiBarberProjectDel, apiProjectList, apiBarberJobList } from '@/api/barber/barber'
import MaterialSelect from '@/components/material-select/index.vue'
@Component({
    name: 'BarberListsDetail',
    components: {
        MaterialSelect
    }
})
export default class BarberListsDetail extends Vue {
    // 表单数据
    private form = {
        id: '',
        name: '',
        mobile: '',
        image: '',
        shop_id: '',
        account: '',
        pass: '',
        jid: ''
    }
    
    // 表单规则
    private rules = {
        name: [{ required: true, message: '请输入理发师名称', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        image: [{ required: true, message: '请上传头像', trigger: 'change' }],
        shop_id: [{ required: true, message: '请选择所属店铺', trigger: 'change' }],
        account: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        pass: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
        jid: [{ required: true, message: '请选择职位', trigger: 'change' }]
    }
    
    // 店铺列表
    private shopList: any[] = []
    
    // 职位列表
    private jobList: any[] = []
    
    // 是否为编辑模式
    private isEdit = false
    
    // 加载状态
    private loading = false
    
    created() {
        this.getShopList()
        this.getJobList()
        // 判断是否为编辑模式
        const id = this.$route.query.id
        if (id) {
            this.isEdit = true
            this.form.id = id as string
            this.getDetail()
        }
    }
    
    // 获取理发师详情
    async getDetail() {
        try {
            this.loading = true
            const res = await apiBarberDetail({ id: this.form.id })
            console.log('res', res)
            
            if (res) {
                this.form = {
                    ...this.form,
                    ...res
                }
                // 编辑模式下，密码字段如果未修改则不需要验证
                if (this.isEdit) {
                    const passwordRule = this.rules.pass[0]
                    passwordRule.required = false
                }
            }
        } catch (error) {
            console.error('获取理发师详情失败', error)
        } finally {
            this.loading = false
        }
    }
    
    // 获取店铺列表
    async getShopList() {
        try {
            const res = await apiSubShopList({})
            if (res && res.lists) {
                this.shopList = res.lists
            }
        } catch (error) {
            console.error('获取店铺列表失败', error)
        }
    }

    // 获取职位列表
    async getJobList() {
        try {
            const res = await apiBarberJobList({
                page_no: 1,
                page_size: 1000
            })
            if (res && res.lists) {
                this.jobList = res.lists
            }
        } catch (error) {
            console.error('获取职位列表失败', error)
        }
    }
    
    // 提交表单
    async submitForm() {
        const formRef = this.$refs.form as any
        if (!formRef) {
return
}
        
        try {
            await formRef.validate()
            
            this.loading = true
            // 如果是编辑模式且密码为空，则不提交密码字段
            const params: any = { ...this.form }
            if (this.isEdit && !params.pass) {
                delete params.pass
            }

            try {
                const res = await apiBarberSave(params)
                console.log('res', res)
                this.$message.success(this.isEdit ? '修改成功' : '新增成功')
                this.goBack()
            } catch (error) {
                console.error('表单验证失败', error)
            } finally {
                this.loading = false
            }
        } catch (error) {
            console.error('表单验证失败', error)
        } finally {
            this.loading = false
        }
    }
    
    // 返回列表
    goBack() {
        this.$router.push('/barber/list')
    }
}
</script>

<template>
    <div class="barber-detail-container" v-loading="loading">
        <!-- 导航头部 -->
        <div class="ls-card">
            <el-page-header @back="$router.go(-1)" :content="isEdit ? '编辑理发师' : '新增理发师'" />
        </div>
        
        <div class="form-container m-t-16">
            <el-form 
                ref="form" 
                :model="form" 
                :rules="rules" 
                label-width="100px"
                size="small"
            >
                <el-form-item label="理发师名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入理发师名称"></el-input>
                </el-form-item>
                
                <el-form-item label="手机号" prop="mobile">
                    <el-input v-model="form.mobile" placeholder="请输入手机号"></el-input>
                </el-form-item>
                
                <el-form-item label="头像" prop="image">
                    <material-select :limit="1" v-model="form.image" />
                </el-form-item>
                
                <el-form-item label="所属店铺" prop="shop_id">
                    <el-select v-model="form.shop_id" placeholder="请选择店铺">
                        <el-option
                            v-for="item in shopList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="职位" prop="jid">
                    <el-select v-model="form.jid" placeholder="请选择职位">
                        <el-option
                            v-for="item in jobList"
                            :key="item.id"
                            :label="item.title"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="登录账号" prop="account">
                    <el-input v-model="form.account" placeholder="请输入登录账号"></el-input>
                </el-form-item>
                
                <el-form-item label="登录密码" prop="pass">
                    <el-input 
                        v-model="form.pass" 
                        placeholder="请输入登录密码" 
                        type="password"
                        show-password
                    ></el-input>
                    <div class="password-tip" v-if="isEdit">不填写则不修改密码</div>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.barber-detail-container {
    padding: 20px;
    
    .page-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }
    
    .form-container {
        background-color: #fff;
        padding: 30px;
        border-radius: 4px;
        
        .el-form {
            max-width: 600px;
        }
    }
    
    .avatar-uploader {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: block;
        }
        
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 120px;
            height: 120px;
            line-height: 120px;
            text-align: center;
            border: 1px dashed #d9d9d9;
            border-radius: 50%;
        }
        
        .upload-tip {
            margin-top: 8px;
            color: #999;
            font-size: 12px;
        }
    }
    
    .password-tip {
        color: #999;
        font-size: 12px;
        margin-top: 5px;
    }
    
    .section-title {
        font-size: 16px;
        font-weight: bold;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px 4px 0 0;
        border-bottom: 1px solid #ebeef5;
    }
    
    .project-container {
        background-color: #fff;
        border-radius: 4px;
        
        .empty-project {
            padding: 30px 0;
        }
    }
    
    .delete-btn {
        color: #F56C6C;
    }
    
    .form-text {
        line-height: 32px;
        font-size: 14px;
        color: #606266;
    }
}
</style>
