/**
 * @description 自定义治理用于平台 设置套餐权限
 * 指令用法 :  <divv-for="(item, index) in linkList" :key="index" v-comboAuth="item.comboAuth">
 * v-comboAuth='xxx' 传入相应的权限字符
 *
 */

import Vue from 'vue'
import store from '@/store'
Vue.directive('comboAuth', {
    inserted(el, binding) {
        if (binding.value) {
            const comboAuth = store.getters.comboAuth
            if (
                !comboAuth.some((item: string) => {
                    return item == binding.value
                })
            ) {
                console.log(el, 'el-----')

                //移除对应的dom节点
                el.parentNode?.removeChild(el)
            }
        }
    }
})
