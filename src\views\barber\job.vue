<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiBarberJobList, apiBarberJobDetail, apiBarberJobSave, apiBarberJobDel } from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'

interface JobItem {
    id: number
    title: string
    commission: string
    create_time: number
    delete_time: number | null
    sid: number
}

@Component({
    name: 'BarberJob',
    components: {
        LsPagination
    }
})
export default class BarberJob extends Vue {
    // 搜索表单
    private searchForm = {
        name: ''
    }
    
    // 分页数据
    private pager = new RequestPaging()
    
    // 职位弹窗
    private jobDialog = {
        visible: false,
        title: '新增职位',
        isEdit: false
    }
    
    // 职位表单
    private jobForm = {
        id: '',
        title: '',
        commission: '',
        price: '', // 任务业绩
        add_price: '', // 每增加多少钱
        add_commission: '' // 每增加的提成  
    }
    
    // 职位表单规则
    private jobRules = {
        title: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],
        commission: [{ required: true, message: '请输入分成比例', trigger: 'blur' }]
    }
    
    // 加载状态
    private loading = false
    
    created() {
        this.getList()
    }
    
    // 获取职位列表
    async getList() {
        try {
            this.loading = true
            await this.pager.request({
                callback: apiBarberJobList,
                params: this.searchForm
            })
        } catch (error) {
            console.error('获取职位列表失败', error)
        } finally {
            this.loading = false
        }
    }
    
    // 搜索
    handleSearch() {
        this.pager.page = 1
        this.getList()
    }
    
    // 重置搜索
    resetSearch() {
        this.searchForm.name = ''
        this.handleSearch()
    }
    
    // 新增职位
    handleAdd() {
        this.jobDialog = {
            visible: true,
            title: '新增职位',
            isEdit: false
        }
        this.jobForm = {
            id: '',
            title: '',
            commission: '',
            price: '',
            add_price: '',
            add_commission: ''
        }
    }
    
    // 编辑职位
    async handleEdit(row: JobItem) {
        this.jobDialog = {
            visible: true,
            title: '编辑职位',
            isEdit: true
        }
        
        try {
            this.loading = true
            const res = await apiBarberJobDetail({ id: row.id })
            if (res) {
                this.jobForm = {
                    id: res.id,
                    title: res.title,
                    commission: res.commission,
                    price: res.price,
                    add_price: res.add_price,
                    add_commission: res.add_commission
                }
            }
        } catch (error) {
            console.error('获取职位详情失败', error)
        } finally {
            this.loading = false
        }
    }
    
    // 删除职位
    async handleDelete(row: JobItem) {
        try {
            await this.$confirm('确认删除该职位?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            
            this.loading = true
            try {
                await apiBarberJobDel({ id: row.id })
                this.$message.success('删除成功')
                this.getList()
            } catch (error) {
                console.error('删除失败:', error)
                this.$message.error('删除失败')
            }
        } catch (error) {
            console.log('取消删除')
        } finally {
            this.loading = false
        }
    }
    
    // 保存职位
    async handleSave() {
        const formRef = this.$refs.jobForm as any
        if (!formRef) {
return
}
        
        try {
            await formRef.validate()
            
            this.loading = true
            try {
                const params = {
                    ...this.jobForm
                }
                
                await apiBarberJobSave(params)
                this.$message.success(this.jobDialog.isEdit ? '修改成功' : '新增成功')
                this.jobDialog.visible = false
                this.getList()
            } catch (error) {
                console.error('保存职位失败', error)
                this.$message.error(this.jobDialog.isEdit ? '修改失败' : '新增失败')
            }
        } catch (error) {
            console.error('表单验证失败', error)
        } finally {
            this.loading = false
        }
    }
    
    // 关闭弹窗
    handleCloseDialog() {
        this.jobDialog.visible = false
    }

    // 格式化分成比例
    // formatCommission(commission: string): string {
    //     const num = parseFloat(commission)
    //     return (num * 100).toFixed(0) + '%'
    // }
}
</script>

<template>
    <div class="barber-job-container" v-loading="loading">
        <!-- 页面标题 -->
        <div class="page-title">理发师职位管理</div>
        
        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="职位名称">
                    <el-input 
                        v-model="searchForm.name" 
                        placeholder="请输入职位名称" 
                        clearable
                        @keyup.enter.native="handleSearch"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        
        <!-- 工具栏 -->
        <div class="tool-container">
            <el-button type="primary" @click="handleAdd">新增职位</el-button>
        </div>
        
        <!-- 表格 -->
        <div class="table-container">
            <el-table
                :data="pager.lists"
                v-loading="pager.loading"
                border
                style="width: 100%"
                size="mini"
            >
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="title" label="职位名称"></el-table-column>
                <el-table-column prop="commission" label="分成比例">
                    <template slot-scope="{ row }">
                        {{ row.commission }}%
                    </template>
                </el-table-column>
                <el-table-column prop="price" label="任务业绩"></el-table-column>
                <el-table-column prop="add_price" label="每增加多少钱"></el-table-column>
                <el-table-column prop="add_commission" label="每增加的提成"></el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="{ row }">
                        <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="text" size="small" @click="handleDelete(row)" class="delete-btn">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-container" v-if="pager.lists.length > 0">
                <ls-pagination v-model="pager" @change="getList()" />
            </div>
            
            <!-- 无数据提示 -->
            <div class="empty-tip" v-if="pager.lists.length === 0">
                <el-empty description="暂无数据"></el-empty>
            </div>
        </div>
        
        <!-- 职位表单弹窗 -->
        <el-dialog
            :title="jobDialog.title"
            :visible.sync="jobDialog.visible"
            width="500px"
            :close-on-click-modal="false"
            @closed="handleCloseDialog"
        >
            <el-form
                ref="jobForm"
                :model="jobForm"
                :rules="jobRules"
                label-width="100px"
                size="small"
            >
                <el-form-item label="职位名称" prop="title">
                    <el-input v-model="jobForm.title" placeholder="请输入职位名称"></el-input>
                </el-form-item>
                
                <el-form-item label="分成比例" prop="commission">
                    <el-input v-model="jobForm.commission" placeholder="请输入分成比例" type="number" step="0.01" min="0" max="1">
                        <template slot="append">%</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="任务业绩" prop="price">
                    <el-input v-model="jobForm.price" placeholder="请输入任务业绩" type="number" step="0.01" min="0"></el-input>
                </el-form-item>
                <el-form-item label="每增加多少钱" prop="add_price">
                    <el-input v-model="jobForm.add_price" placeholder="请输入每增加多少钱" type="number" step="0.01" min="0"></el-input>
                </el-form-item>
                <el-form-item label="每增加的提成" prop="add_commission">
                    <el-input v-model="jobForm.add_commission" placeholder="请输入每增加的提成" type="number" step="0.01" min="0"></el-input>
                </el-form-item>
                
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button @click="jobDialog.visible = false">取 消</el-button>
                <el-button type="primary" @click="handleSave" :loading="loading">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.barber-job-container {
    padding: 20px;
    
    .page-title {
        font-size: 20px;
        font-weight: bold;
        // margin-bottom: 20px;
        background-color: #fff;
        padding: 10px 20px;
        border-radius: 20px 20px 0 0;
    }
    
    .search-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 0 0 20px 20px;
        margin-bottom: 20px;
    }
    
    .tool-container {
        margin-bottom: 20px;
    }
    
    .table-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 4px;
        
        .delete-btn {
            color: #F56C6C;
        }
    }
    
    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
    
    .empty-tip {
        padding: 40px 0;
    }
}
</style> 