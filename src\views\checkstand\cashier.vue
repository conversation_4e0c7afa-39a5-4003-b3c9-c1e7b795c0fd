<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import {
    apiCheckstandList,
    apiCheckstandServiceList,
    apiCheckstandUserList,
    apiCheckstandUserCardList,
    apiCheckstandGoodsList,
    apiShopList,
    apiCheckstandPay
} from '@/api/checkstand/checkstand'
import { apiUserLevelList } from '@/api/user/user'
import SlipDialog from '@/components/slip-dialog/index.vue'
import { getImageUrl } from '@/utils/util'

@Component({
    name: 'Cashier',
    components: {
        SlipDialog
    }
})
export default class Cashier extends Vue {
    // 加载状态
    private loading = {
        barber: false,
        service: false,
        user: false,
        card: false,
        goods: false,
        hairWashBarber: false,
        allGoods: false
    }

    // 当前选择的理发师ID
    private selectedBarberId = ''

    // 当前选择的理发师名称
    private selectedBarberName = ''

    // 当前选择的洗头发理发师ID
    private selectedHairWashBarberId = ''

    // 洗头发理发师名称
    private hairWashBarberName = ''

    // 当前选择的用户ID
    private selectedUserId = ''

    // 搜索表单
    private searchForm = {
        barberName: '',
        mobile: '',
        goodsName: '',
        hairWashBarberName: ''
    }

    // 理发师列表
    private barberList: any[] = []

    // 洗头发理发师列表
    private hairWashBarberList: any[] = []

    // 服务项目列表
    private serviceList: any[] = []

    // 选中的服务项目
    private selectedServices: any[] = []

    // 用户信息
    private userInfo: any = null

    // 用户会员卡
    private userCards: any[] = []

    // 商品列表（搜索结果）
    private goodsList: any[] = []

    // 所有商品列表（用于展示）
    private allGoodsList: any[] = []

    // 选中的商品
    private selectedGoods: any[] = []

    // 支付方式
    private paymentMethod = 'cash'

    // 支付方式选项
    private paymentOptions = [
        { label: '现金', value: 'cash', payType: 1, icon: '💵', color: '#67C23A' },
        { label: '微信', value: 'wechat', payType: 2, icon: '💬', color: '#07C160' },
        { label: '支付宝', value: 'alipay', payType: 3, icon: '🅰️', color: '#1677FF' },
        { label: '银行卡', value: 'bankcard', payType: 4, icon: '💳', color: '#409EFF' },
        { label: '会员卡余额', value: 'memberCard', payType: 6, icon: '💰', color: '#E6A23C' },
        { label: '会员卡次数', value: 'countCard', payType: 7, icon: '🎫', color: '#909399' },
        { label: '抖音支付', value: 'douyin', payType: 8, icon: '🎵', color: '#FE2C55' },
        { label: '美团支付', value: 'meituan', payType: 9, icon: '🛵', color: '#FFD100' }
    ]

    // 结算方式（自动计算或手动输入）
    private settleMethod = 'auto'

    // 手动输入金额
    private manualAmount = 0

    // 会员查询弹框显示状态
    private memberDialogVisible = false

    // 理发师选择弹框显示状态
    private barberDialogVisible = false

    // 所有理发师列表（用于弹框初始显示）
    private allBarberList: any[] = []

    // 所有洗发师列表（用于弹框初始显示）
    private allHairWashBarberList: any[] = []

    // 洗发师选择弹框显示状态
    private hairWashBarberDialogVisible = false

    // 弹框中的搜索表单
    private memberSearchForm = {
        mobile: '',
        nickname: ''
    }

    // 理发师弹框搜索表单
    private barberDialogSearchForm = {
        name: ''
    }

    // 洗发师弹框搜索表单
    private hairWashBarberDialogSearchForm = {
        name: ''
    }

    // 会员搜索结果
    private memberSearchResults: any[] = []

    // 用户等级列表
    private userLevelList: any[] = []

    // 支付相关
    private paymentLoading = false
    private selectedCardId = '' // 选中的会员卡ID

    // 水单弹框相关
    private slipDialogVisible = false
    private slipData: any = null
    private slipStatistics: any = null

    // 订单总金额
    private get totalAmount(): number {
        let total = 0

        // 计算服务项目金额（使用当前价格）
        this.selectedServices.forEach(service => {
            const price = service.currentPrice !== undefined ? service.currentPrice : parseFloat(service.price || 0)
            total += price * (service.quantity || 1)
        })

        // 计算商品金额（使用当前价格）
        this.selectedGoods.forEach(goods => {
            const price = goods.currentPrice !== undefined ? goods.currentPrice : parseFloat(goods.sell_price || 0)
            total += price * (goods.quantity || 1)
        })

        return parseFloat(total.toFixed(2))
    }

    // 会员价总金额
    private get vipTotalAmount(): number {
        // 直接返回totalAmount，因为currentPrice已经考虑了会员价
        return this.totalAmount
    }

    // 实际结算金额
    private get actualAmount(): number {
        return this.settleMethod === 'auto'
            ? this.userInfo
                ? this.vipTotalAmount
                : this.totalAmount
            : this.manualAmount
    }

    // 可用的会员卡
    private get availableCards(): any[] {
        if (!this.userInfo || !this.userCards) {
            return []
        }

        if (this.paymentMethod === 'memberCard') {
            return this.userCards.filter(card => card.type === 1)
        } else if (this.paymentMethod === 'countCard') {
            return this.userCards.filter(card => card.type === 2)
        }

        return []
    }

    // 监听理发师ID变化，获取服务项目
    @Watch('selectedBarberId')
    onBarberChange(newVal: string) {
        if (newVal) {
            this.getServiceList(newVal)
        } else {
            this.serviceList = []
            this.selectedServices = []
        }
    }

    // 监听用户ID变化，获取会员卡并刷新商品价格
    @Watch('selectedUserId')
    onUserChange(newVal: string) {
        if (newVal) {
            this.getUserCards(newVal)
        } else {
            this.userCards = []
        }
    }

    // 监听用户信息变化，刷新商品价格
    @Watch('userInfo', { deep: true })
    onUserInfoChange(newVal: any, oldVal: any) {
        // 当用户信息变化时，刷新商品列表以获取对应的会员价格
        if (newVal?.vip_id !== oldVal?.vip_id) {
            this.refreshGoodsList()
        }
    }

    // 页面加载时获取所有商品
    async mounted() {
        await this.getAllGoods()
        await this.getUserLevelList()
    }

    // 获取所有商品
    async getAllGoods() {
        this.loading.allGoods = true
        try {
            this.allGoodsList = await this.getAllGoodsData()
        } catch (error) {
            console.error('获取商品列表失败', error)
            this.$message.error('获取商品列表失败')
        } finally {
            this.loading.allGoods = false
        }
    }

    // 刷新商品列表（当会员变化时调用）
    async refreshGoodsList() {
        // 如果当前没有搜索关键词，刷新所有商品
        if (!this.searchForm.goodsName.trim()) {
            await this.getAllGoods()
        } else {
            // 如果有搜索关键词，重新搜索
            await this.searchGoods()
        }
    }

    // 获取用户等级列表
    async getUserLevelList() {
        try {
            const response = await apiUserLevelList({})
            if (response && response.lists) {
                this.userLevelList = response.lists.sort((a: any, b: any) => a.rank - b.rank)
                console.log('用户等级列表:', this.userLevelList)
            }
        } catch (error) {
            console.error('获取用户等级列表失败:', error)
        }
    }

    // 搜索理发师
    async searchBarber() {
        console.log(this.searchForm.barberName)

        if (!this.searchForm.barberName.trim()) {
            this.barberList = []
            return
        }

        this.loading.barber = true
        try {
            const data = await apiCheckstandList({ name: this.searchForm.barberName })
            this.barberList = data || []
        } catch (error) {
            console.error('获取理发师列表失败', error)
            this.$message.error('获取理发师列表失败')
        } finally {
            this.loading.barber = false
        }
    }

    // 获取服务项目
    async getServiceList(barberId: string) {
        this.loading.service = true
        try {
            const data = await apiCheckstandServiceList({ bid: barberId })
            this.serviceList = data || []
        } catch (error) {
            console.error('获取服务项目失败', error)
            this.$message.error('获取服务项目失败')
        } finally {
            this.loading.service = false
        }
    }

    // 选择理发师
    selectBarber(barber: any) {
        this.selectedBarberId = barber.id
        this.selectedBarberName = barber.name
        this.barberList = []
        this.searchForm.barberName = barber.name
    }

    // 搜索用户
    async searchUser() {
        if (!this.searchForm.mobile.trim()) {
            this.$message.warning('请输入手机号')
            return
        }

        this.loading.user = true
        try {
            const data = await apiCheckstandUserList({ mobile: this.searchForm.mobile })
            if (data && data.length > 0) {
                this.userInfo = data[0]
                this.selectedUserId = this.userInfo.id
            } else {
                this.userInfo = null
                this.selectedUserId = ''
                this.userCards = []
                this.$message.warning('未找到该用户')
            }
        } catch (error) {
            console.error('获取用户信息失败', error)
            this.$message.error('获取用户信息失败')
        } finally {
            this.loading.user = false
        }
    }

    // 打开会员查询弹框
    openMemberDialog() {
        this.memberDialogVisible = true
        this.memberSearchForm.mobile = ''
        this.memberSearchForm.nickname = ''
    }

    // 关闭会员查询弹框
    closeMemberDialog() {
        this.memberDialogVisible = false
        this.memberSearchForm.mobile = ''
        this.memberSearchForm.nickname = ''
        this.memberSearchResults = []
    }

    // 打开理发师选择弹框
    openBarberDialog() {
        this.barberDialogVisible = true
        this.barberDialogSearchForm.name = ''
        this.barberList = []
        // 加载所有理发师列表
        this.getAllBarberList()
    }

    // 关闭理发师选择弹框
    closeBarberDialog() {
        this.barberDialogVisible = false
        this.barberDialogSearchForm.name = ''
        this.barberList = []
    }

    // 获取所有理发师列表
    async getAllBarberList() {
        this.loading.barber = true
        try {
            const data = await apiCheckstandList({})
            this.allBarberList = data || []
            // 如果没有搜索条件，显示所有理发师
            if (!this.barberDialogSearchForm.name.trim()) {
                this.barberList = this.allBarberList
            }
        } catch (error) {
            console.error('获取理发师列表失败', error)
            this.$message.error('获取理发师列表失败')
        } finally {
            this.loading.barber = false
        }
    }

    // 弹框中搜索理发师
    async searchBarberInDialog() {
        if (!this.barberDialogSearchForm.name.trim()) {
            // 如果搜索为空，显示所有理发师
            this.barberList = this.allBarberList
            return
        }

        this.loading.barber = true
        try {
            const data = await apiCheckstandList({ name: this.barberDialogSearchForm.name })
            this.barberList = data || []
        } catch (error) {
            console.error('获取理发师列表失败', error)
            this.$message.error('获取理发师列表失败')
        } finally {
            this.loading.barber = false
        }
    }

    // 在弹框中选择理发师
    selectBarberInDialog(barber: any) {
        this.selectedBarberId = barber.id
        this.selectedBarberName = barber.name
        this.barberDialogVisible = false
        this.getServiceList(barber.id)
    }

    // 打开洗发师选择弹框
    openHairWashBarberDialog() {
        this.hairWashBarberDialogVisible = true
        this.hairWashBarberDialogSearchForm.name = ''
        this.hairWashBarberList = []
        // 加载所有洗发师列表
        this.getAllHairWashBarberList()
    }

    // 关闭洗发师选择弹框
    closeHairWashBarberDialog() {
        this.hairWashBarberDialogVisible = false
        this.hairWashBarberDialogSearchForm.name = ''
        this.hairWashBarberList = []
    }

    // 获取所有洗发师列表
    async getAllHairWashBarberList() {
        this.loading.hairWashBarber = true
        try {
            const data = await apiCheckstandList({})
            this.allHairWashBarberList = data || []
            // 如果没有搜索条件，显示所有洗发师
            if (!this.hairWashBarberDialogSearchForm.name.trim()) {
                this.hairWashBarberList = this.allHairWashBarberList
            }
        } catch (error) {
            console.error('获取洗发师列表失败', error)
            this.$message.error('获取洗发师列表失败')
        } finally {
            this.loading.hairWashBarber = false
        }
    }

    // 弹框中搜索洗发师
    async searchHairWashBarberInDialog() {
        if (!this.hairWashBarberDialogSearchForm.name.trim()) {
            // 如果搜索为空，显示所有洗发师
            this.hairWashBarberList = this.allHairWashBarberList
            return
        }

        this.loading.hairWashBarber = true
        try {
            const data = await apiCheckstandList({ name: this.hairWashBarberDialogSearchForm.name })
            this.hairWashBarberList = data || []
        } catch (error) {
            console.error('获取洗发师列表失败', error)
            this.$message.error('获取洗发师列表失败')
        } finally {
            this.loading.hairWashBarber = false
        }
    }

    // 在弹框中选择洗发师
    selectHairWashBarberInDialog(barber: any) {
        this.selectedHairWashBarberId = barber.id
        this.hairWashBarberName = barber.name
        this.hairWashBarberDialogVisible = false
        this.selectHairWashBarber(barber)
    }

    // 弹框中搜索会员
    async searchMemberInDialog() {
        if (!this.memberSearchForm.mobile.trim() && !this.memberSearchForm.nickname.trim()) {
            this.$message.warning('请输入手机号、会员编码或用户昵称')
            return
        }

        this.loading.user = true
        try {
            const params: any = {}
            if (this.memberSearchForm.mobile.trim()) {
                params.mobile = this.memberSearchForm.mobile
            }
            if (this.memberSearchForm.nickname.trim()) {
                params.nickname = this.memberSearchForm.nickname
            }

            const data = await apiCheckstandUserList(params)
            if (data && data.length > 0) {
                this.memberSearchResults = data
                // 如果只有一个结果，直接选择
                if (data.length === 1) {
                    this.selectMemberFromResults(data[0])
                }
            } else {
                this.memberSearchResults = []
                this.$message.warning('未找到该会员')
            }
        } catch (error) {
            console.error('获取会员信息失败', error)
            this.$message.error('获取会员信息失败')
            this.memberSearchResults = []
        } finally {
            this.loading.user = false
        }
    }

    // 从搜索结果中选择会员
    selectMemberFromResults(member: any) {
        this.userInfo = member
        this.selectedUserId = member.vip_id || member.uid || member.user_id
        this.memberDialogVisible = false
        this.memberSearchResults = []
        this.memberSearchForm.mobile = ''
        this.memberSearchForm.nickname = ''
        this.$message.success('会员选择成功')
    }

    // 清除会员信息
    clearMember() {
        this.userInfo = null
        this.selectedUserId = ''
        this.userCards = []
        this.memberDialogVisible = false
        this.memberSearchResults = []
        this.memberSearchForm.mobile = ''
        this.memberSearchForm.nickname = ''
        this.$message.success('已清除会员信息')
    }

    // 选择支付方式
    selectPaymentMethod(option: any) {
        // 检查是否被禁用
        const isDisabled = (option.value === 'memberCard' && (!this.userInfo || !this.userCards.some((card: any) => card.type === 1))) ||
            (option.value === 'countCard' && (!this.userInfo || !this.userCards.some((card: any) => card.type === 2)))

        if (isDisabled) {
            if (option.value === 'memberCard') {
                this.$message.warning('请先选择会员并确保有可用的会员卡余额')
            } else if (option.value === 'countCard') {
                this.$message.warning('请先选择会员并确保有可用的会员卡次数')
            }
            return
        }

        this.paymentMethod = option.value
        this.selectedCardId = '' // 重置卡片选择
    }

    // 添加新会员（暂时只是提示，可以后续扩展）
    addNewMember() {
        this.$message.info('添加新会员功能待开发')
        // 这里可以跳转到添加会员页面或打开添加会员弹框
    }

    // 显示会员卡详情
    showCardDetails(card: any) {
        const cardType = card.type === 1 ? '会员卡' : card.title
        const cardInfo = card.type === 1
            ? `余额: ¥${card.remain_price}`
            : `剩余次数: ${card.remain_num}次`

        this.$message({
            message: `${cardType} - ${cardInfo}`,
            type: 'info',
            duration: 2000
        })
    }

    // 获取用户会员卡
    async getUserCards(userId: string) {
        this.loading.card = true
        try {
            const data = await apiCheckstandUserCardList({ vuid: userId })
            this.userCards = data || []
        } catch (error) {
            console.error('获取会员卡失败', error)
            this.$message.error('获取会员卡失败')
        } finally {
            this.loading.card = false
        }
    }

    // 搜索商品
    async searchGoods() {
        if (!this.searchForm.goodsName.trim()) {
            // 如果搜索为空，显示所有商品
            this.allGoodsList = await this.getAllGoodsData()
            return
        }

        this.loading.allGoods = true
        try {
            const params: any = { name: this.searchForm.goodsName }
            // 如果有选中的会员，传入会员的vip_id以获取会员价格
            if (this.userInfo && this.userInfo.vip_id) {
                params.uid = this.userInfo.vip_id
            }

            const response = await apiCheckstandGoodsList(params)
            // 适配新的数据结构，返回 items 数组
            const items = response?.items || []

            // 去重处理，基于 id 去重
            this.allGoodsList = items.filter((item: any, index: number, self: any[]) =>
                index === self.findIndex((t: any) => t.id === item.id)
            )
        } catch (error) {
            console.error('搜索商品失败', error)
            this.$message.error('搜索商品失败')
        } finally {
            this.loading.allGoods = false
        }
    }

    // 获取所有商品数据
    async getAllGoodsData() {
        try {
            const params: any = {}
            // 如果有选中的会员，传入会员的vip_id以获取会员价格
            if (this.userInfo && this.userInfo.vip_id) {
                params.uid = this.userInfo.vip_id
            }

            const response = await apiShopList(params)
            // 适配新的数据结构，返回 items 数组
            const items = response?.items || []

            // 去重处理，基于 id 去重
            const uniqueItems = items.filter((item: any, index: number, self: any[]) =>
                index === self.findIndex((t: any) => t.id === item.id)
            )

            return uniqueItems
        } catch (error) {
            console.error('获取商品列表失败', error)
            return []
        }
    }

    // 添加服务项目
    addService(service: any) {
        // 检查是否已经添加过该服务
        const existIndex = this.selectedServices.findIndex(item => item.id === service.id)

        if (existIndex > -1) {
            // 已存在则增加数量
            this.selectedServices[existIndex].quantity += 1
        } else {
            // 确定初始价格（会员价优先）
            const initialPrice = this.userInfo && service.vip_price
                ? parseFloat(service.vip_price)
                : parseFloat(service.price)

            // 不存在则添加新服务，并设置初始数量为1和当前价格
            this.selectedServices.push({
                ...service,
                quantity: 1,
                currentPrice: initialPrice
            })
        }
    }

    // 移除服务项目
    removeService(index: number) {
        this.selectedServices.splice(index, 1)
    }

    // 更改服务项目数量
    changeServiceQuantity(index: number, quantity: number) {
        if (quantity < 1) {
            quantity = 1
        }
        this.selectedServices[index].quantity = quantity
    }

    // 修改服务项目价格
    changeServicePrice(index: number, price: number) {
        if (price < 0) {
            price = 0
        }
        this.selectedServices[index].currentPrice = price
    }

    // 添加商品
    addGoods(goodsItem: any) {
        // 适配新的数据结构
        const good = {
            id: goodsItem.id,
            name: goodsItem.title, // 新结构中使用 title 字段
            title: goodsItem.title,
            image: goodsItem.image,
            sell_price: goodsItem.current_price || goodsItem.original_price, // 使用当前价格或原价
            member_price: goodsItem.member_price, // 会员价格
            stock: goodsItem.stock,
            item_type: goodsItem.item_type,
            category_name: goodsItem.category_name
        }

        // 检查是否已经添加过该商品
        const existIndex = this.selectedGoods.findIndex(item => item.id === good.id)

        if (existIndex > -1) {
            // 已存在则增加数量
            this.selectedGoods[existIndex].quantity += 1
        } else {
            // 确定初始价格（会员价优先，如果有会员信息且商品支持会员价的话）
            const initialPrice = this.userInfo && this.userInfo.vip_id && good.member_price && goodsItem.is_member_price
                ? parseFloat(good.member_price)
                : parseFloat(good.sell_price)

            // 不存在则添加新商品，并设置初始数量为1和当前价格
            this.selectedGoods.push({
                ...good,
                quantity: 1,
                currentPrice: initialPrice
            })
        }

        this.goodsList = []
        this.searchForm.goodsName = ''
    }

    // 移除商品
    removeGoods(index: number) {
        this.selectedGoods.splice(index, 1)
    }

    // 更改商品数量
    changeGoodsQuantity(index: number, quantity: number) {
        if (quantity < 1) {
            quantity = 1
        }
        this.selectedGoods[index].quantity = quantity
    }

    // 修改商品价格
    changeGoodsPrice(index: number, price: number) {
        if (price < 0) {
            price = 0
        }
        this.selectedGoods[index].currentPrice = price
    }

    // 切换结算方式时的处理
    @Watch('settleMethod')
    onSettleMethodChange(newMethod: string) {
        if (newMethod === 'manual') {
            // 切换到手动输入时，自动填入当前计算的金额
            this.manualAmount = this.userInfo ? this.vipTotalAmount : this.totalAmount
        }
    }

    // 应用折扣
    applyDiscount(discount: number) {
        const baseAmount = this.userInfo ? this.vipTotalAmount : this.totalAmount
        this.manualAmount = parseFloat((baseAmount * discount).toFixed(2))
    }

    // 重置为计算金额
    resetToCalculated() {
        this.manualAmount = this.userInfo ? this.vipTotalAmount : this.totalAmount
    }

    // 搜索洗头发理发师
    async searchHairWashBarber() {
        if (!this.searchForm.hairWashBarberName.trim()) {
            this.hairWashBarberList = []
            return
        }

        this.loading.hairWashBarber = true
        try {
            const data = await apiCheckstandList({ name: this.searchForm.hairWashBarberName })
            this.hairWashBarberList = data || []
        } catch (error) {
            console.error('获取洗头发理发师列表失败', error)
            this.$message.error('获取洗头发理发师列表失败')
        } finally {
            this.loading.hairWashBarber = false
        }
    }

    // 选择洗头发理发师
    selectHairWashBarber(barber: any) {
        this.selectedHairWashBarberId = barber.id
        this.hairWashBarberName = barber.name
        this.hairWashBarberList = []
        this.searchForm.hairWashBarberName = barber.name

        // 只记录洗发师信息，不添加到订单项目中
        // 洗发师信息将在支付时作为 shampoo_bid 参数传递
    }



    // 提交订单
    async submitOrder() {
        // 验证必填项
        if (!this.validateOrder()) {
            return
        }

        // 确认支付
        try {
            await this.$confirm('确认提交订单并支付吗?', '确认支付', {
                confirmButtonText: '确认支付',
                cancelButtonText: '取消',
                type: 'warning'
            })

            await this.processPayment()
        } catch (error) {
            if (error !== 'cancel') {
                console.error('支付失败:', error)
            }
        }
    }

    // 验证订单
    validateOrder(): boolean {
        if (!this.selectedBarberId) {
            this.$message.warning('请选择理发师')
            return false
        }

        if (this.selectedServices.length === 0 && this.selectedGoods.length === 0) {
            this.$message.warning('请至少选择一项服务或商品')
            return false
        }

        if (this.settleMethod === 'manual' && this.manualAmount <= 0) {
            this.$message.warning('手动输入金额必须大于0')
            return false
        }

        // 验证会员卡支付
        if (this.paymentMethod === 'memberCard') {
            if (!this.userInfo) {
                this.$message.warning('使用会员卡支付需要先选择会员')
                return false
            }
            if (!this.selectedCardId) {
                this.$message.warning('请选择要使用的会员卡')
                return false
            }
            const selectedCard = this.userCards.find(card => card.id == this.selectedCardId)
            if (!selectedCard) {
                this.$message.warning('选择的会员卡无效')
                return false
            }
            if (parseFloat(selectedCard.remain_price) < this.actualAmount) {
                this.$message.warning('会员卡余额不足')
                return false
            }
        }

        // 验证次卡支付
        if (this.paymentMethod === 'countCard') {
            if (!this.userInfo) {
                this.$message.warning('使用次卡支付需要先选择会员')
                return false
            }
            if (!this.selectedCardId) {
                this.$message.warning('请选择要使用的次卡')
                return false
            }
            const selectedCard = this.userCards.find(card => card.id == this.selectedCardId)
            if (!selectedCard) {
                this.$message.warning('选择的次卡无效')
                return false
            }
            if (parseInt(selectedCard.remain_num) <= 0) {
                this.$message.warning('次卡剩余次数不足')
                return false
            }
        }

        return true
    }

    // 处理支付
    async processPayment() {
        this.paymentLoading = true

        try {
            // 构建支付参数
            const paymentData = this.buildPaymentData()

            // 调用支付接口
            const result = await apiCheckstandPay(paymentData)

            // 支付成功，显示水单弹框
            if (result && result.slip_detail) {
                this.slipData = result.slip_detail
                this.slipStatistics = result.statistics
                this.slipDialogVisible = true
            } else {
                // 如果没有返回水单数据，显示简单的成功提示
                this.$message.success('支付成功！')
            }

            this.resetForm()

            // 可以在这里添加打印小票等功能
            // console.log('支付结果:', result)

        } catch (error) {
            console.error('支付失败:', error)
            this.$message.error('支付失败，请重试')
            throw error
        } finally {
            this.paymentLoading = false
        }
    }

    // 构建支付数据
    buildPaymentData() {
        // 获取支付方式对应的 payType
        const paymentOption = this.paymentOptions.find(option => option.value === this.paymentMethod)
        const payType = paymentOption ? paymentOption.payType : 1

        // 构建服务项目标题
        const serviceTitles = this.selectedServices.map(service => service.services_title).join(',')

        // 获取会员卡ID（如果使用会员卡或次卡支付）
        let cardId = ''
        if (this.paymentMethod === 'memberCard' || this.paymentMethod === 'countCard') {
            cardId = this.selectedCardId
        }

        // 构建订单项目列表
        const orderItems: any[] = []

        // 添加服务项目
        this.selectedServices.forEach(service => {
            orderItems.push({
                item_id: service.id,
                item_type: 'service',
                title: service.services_title,
                original_price: parseFloat(service.price || service.vip_price || '0'),
                current_price: parseFloat(service.currentPrice.toString()),
                quantity: service.quantity || 1
            })
        })

        // 添加商品项目
        this.selectedGoods.forEach(goods => {
            orderItems.push({
                item_id: goods.id,
                item_type: 'goods',
                title: goods.name || goods.title,
                original_price: parseFloat(goods.sell_price || '0'),
                current_price: parseFloat(goods.currentPrice.toString()),
                quantity: goods.quantity || 1
            })
        })

        const paymentData: any = {
            bid: this.selectedBarberId, // 理发师ID
            service_title: serviceTitles, // 服务项目标题
            shampoo_bid: this.selectedHairWashBarberId || '', // 洗头理发师ID
            price: this.actualAmount, // 支付金额
            pay_type: payType, // 支付方式
            uid: this.userInfo ? (this.userInfo.vip_id || this.userInfo.uid || this.userInfo.user_id || 0) : 0, // 用户ID，0为散客
            order_items: orderItems // 订单项目列表（数组格式）
        }

        // 添加可选参数
        if (cardId) {
            paymentData.card_id = cardId
        }

        return paymentData
    }

    // 会员卡显示文本
    cardDisplayText(card: any): string {
        if (card.type === 1) {
            // 会员卡显示余额
            return `会员卡 (余额: ¥${card.remain_price})`
        } else if (card.type === 2) {
            // 次卡显示剩余次数
            return `${card.title} (剩余: ${card.remain_num}次)`
        }
        return card.title || '未知卡片'
    }

    // 水单弹框关闭处理
    handleSlipDialogClose() {
        this.slipDialogVisible = false
        this.slipData = null
        this.slipStatistics = null
    }

    // 打印水单处理
    handleSlipPrint(slipData: any) {
        // 这里可以添加打印逻辑
        this.$message.info('打印功能待开发')
    }

    // 处理图片URL
    getImageUrl(imagePath: string): string {
        return getImageUrl(imagePath)
    }


    // 获取头像等级样式类
    getAvatarLevelClass(userInfo: any): string {
        if (!userInfo || !userInfo.level_info) {
return ''
}
        return this.getLevelStyleClass(userInfo.level_info, 'avatar')
    }

    // 获取皇冠样式类
    getCrownClass(levelInfo: any): string {
        if (!levelInfo) {
return 'crown-default'
}
        return this.getLevelStyleClass(levelInfo, 'crown')
    }

    // 获取等级徽章样式类
    getLevelBadgeClass(levelInfo: any): string {
        console.log('levelInfo:', levelInfo)
        if (!levelInfo) {
return 'badge-default'
}
        return this.getLevelStyleClass(levelInfo, 'badge')
    }

    // 根据等级信息获取样式类（智能分配）
    getLevelStyleClass(levelInfo: any, prefix: string): string {
        if (!levelInfo || !this.userLevelList.length) {
return `${prefix}-default`
}

        // 查找当前等级在等级列表中的信息
        const currentLevel = this.userLevelList.find(level => level.id === levelInfo.level_id)
        if (!currentLevel) {
return `${prefix}-default`
}

        // 智能主题分配：优先根据等级名称匹配，其次根据 rank
        const levelName = currentLevel.name.toLowerCase()
        let theme = 'default'

        // 根据等级名称智能匹配主题
        if (levelName.includes('普通') || levelName.includes('bronze') || levelName.includes('青铜')) {
            theme = 'bronze'
        } else if (levelName.includes('白银') || levelName.includes('silver')) {
            theme = 'silver'
        } else if (levelName.includes('黄金') || levelName.includes('gold')) {
            theme = 'gold'
        } else if (levelName.includes('铂金') || levelName.includes('platinum')) {
            theme = 'platinum'
        } else if (levelName.includes('钻石') || levelName.includes('diamond')) {
            theme = 'diamond'
        } else if (levelName.includes('红宝石') || levelName.includes('ruby')) {
            theme = 'ruby'
        } else if (levelName.includes('翡翠') || levelName.includes('emerald')) {
            theme = 'emerald'
        } else if (levelName.includes('蓝宝石') || levelName.includes('sapphire')) {
            theme = 'sapphire'
        } else {
            // 如果名称匹配不到，根据 rank 分配
            const levelRank = currentLevel.rank || levelInfo.level_rank || 1
            const colorThemes = ['bronze', 'silver', 'gold', 'platinum', 'diamond', 'ruby', 'emerald', 'sapphire']
            const themeIndex = (levelRank - 1) % colorThemes.length
            theme = colorThemes[themeIndex]
        }

        console.log('等级样式分配:', {
            levelId: levelInfo.level_id,
            levelName: currentLevel.name,
            levelRank: currentLevel.rank,
            matchedTheme: theme,
            finalClass: `${prefix}-${theme}`
        })

        return `${prefix}-${theme}`
    }

    // 获取等级折扣显示
    getLevelDiscount(levelInfo: any): string {
        if (!levelInfo) {
return '10'
}

        // 查找对应的等级信息
        const currentLevel = this.userLevelList.find(level => level.id === levelInfo.level_id)
        if (!currentLevel) {
return '10'
}

        // 如果等级数据中有折扣信息，优先使用
        if (currentLevel.discount && currentLevel.discount !== '无') {
            // 处理 "9.5折" 这样的格式
            const discountMatch = currentLevel.discount.match(/(\d+\.?\d*)折/)
            if (discountMatch) {
                return discountMatch[1]
            }
        }

        // 如果level_info中有折扣信息，作为备选
        if (levelInfo.level_discount) {
            return (levelInfo.level_discount * 10).toFixed(1)
        }

        return '10'
    }

    // 重置表单
    resetForm() {
        this.selectedBarberId = ''
        this.selectedHairWashBarberId = ''
        this.hairWashBarberName = ''
        this.selectedUserId = ''
        this.searchForm = {
            barberName: '',
            mobile: '',
            goodsName: '',
            hairWashBarberName: ''
        }
        this.barberList = []
        this.hairWashBarberList = []
        this.serviceList = []
        this.selectedServices = []
        this.userInfo = null
        this.userCards = []
        this.goodsList = []
        this.selectedGoods = []
        this.paymentMethod = 'cash'
        this.settleMethod = 'auto'
        this.manualAmount = 0
        this.selectedCardId = ''
        this.paymentLoading = false
        this.memberSearchResults = []
        this.selectedBarberName = ''
    }
}
</script>

<template>
    <div class="cashier-container">
        <div class="cashier-content">
            <!-- 左侧：客户信息和选择区域 -->
            <div class="left-panel">
                <!-- 客户信息 -->
                <div class="panel-section customer-section">
                    <!-- 简化的客户信息卡片 -->
                    <div class="compact-customer-card" @click="openMemberDialog">
                        <div class="customer-avatar" :class="getAvatarLevelClass(userInfo)">
                            <div class="avatar-crown" v-if="userInfo && userInfo.level_info">
                                <i class="crown-icon" :class="getCrownClass(userInfo.level_info)"></i>
                            </div>
                            <el-image
                                v-if="userInfo && userInfo.avatar"
                                :src="userInfo.avatar"
                                fit="cover"
                                class="avatar-image"
                            >
                                <div slot="error" class="avatar-fallback">
                                    <i class="el-icon-user-solid"></i>
                                </div>
                            </el-image>
                            <i v-else class="el-icon-user-solid"></i>
                        </div>
                        <div class="customer-info">
                            <div class="customer-name" v-if="!userInfo">游客</div>
                            <div class="customer-name" v-else>
                                {{ userInfo.nickname || userInfo.mobile }}
                                <span class="customer-gender"
                                      :class="{ 'male': userInfo.sex === 0, 'female': userInfo.sex === 1 }">
                                    {{ userInfo.sex === 0 ? '♂' : '♀' }}
                                </span>
                                <!-- 会员等级标签 -->
                                <span v-if="userInfo.level_info"
                                      class="member-level-badge"
                                      :class="getLevelBadgeClass(userInfo.level_info)">
                                    {{ userInfo.level_info.level_name }}
                                </span>
                            </div>
                            <div class="customer-status" v-if="!userInfo">点击查询会员</div>
                            <div class="customer-status" v-else>
                                <span v-if="userInfo.nickname && userInfo.mobile">{{ userInfo.mobile }} | </span>
                                积分: {{ userInfo.points }}
                                <span v-if="userInfo.level_info"> | {{ getLevelDiscount(userInfo.level_info) }}折</span>
                                | 点击更换
                            </div>
                        </div>
                        <div class="customer-action">
                            <i class="el-icon-search"></i>
                        </div>
                    </div>

                    <!-- 会员卡快速显示 -->
                    <div class="quick-cards" v-if="userInfo && userCards.length > 0" v-loading="loading.card">
                        <div
                            v-for="(card, index) in userCards"
                            :key="`card-${card.id}-${index}`"
                            class="quick-card-item"
                            :class="{
                                'quick-card-member': card.type === 1,
                                'quick-card-count': card.type === 2
                            }"
                            @click="showCardDetails(card)"
                        >
                            <div class="quick-card-type">{{ card.type === 1 ? '会员卡' : card.title }}</div>
                            <div class="quick-card-value" v-if="card.type === 1">¥{{ card.remain_price }}</div>
                            <div class="quick-card-value" v-else>{{ card.remain_num }}次</div>
                        </div>
                    </div>
                </div>

                <!-- 已选项目/商品 -->
                <div class="panel-section cart-section">
                    <div class="section-title">已选项目/商品</div>
                    <div class="cart-container">
                        <!-- 已选服务项目 -->
                        <div class="cart-services" v-if="selectedServices.length > 0">
                            <div class="cart-title">服务项目</div>
                            <div
                                v-for="(service, index) in selectedServices"
                                :key="`service-${index}`"
                                class="cart-item"
                            >
                                <div class="item-header">
                                    <div class="item-name">{{ service.services_title }}</div>
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        icon="el-icon-delete"
                                        circle
                                        @click="removeService(index)"
                                        class="delete-btn"
                                    ></el-button>
                                </div>

                                <div class="item-details">
                                    <div class="price-section">
                                        <label class="field-label">价格</label>
                                        <div class="price-input-wrapper">
                                            <el-input-number
                                                v-model="service.currentPrice"
                                                :min="0"
                                                :precision="2"
                                                size="small"
                                                :controls="false"
                                                class="price-input"
                                            ></el-input-number>
                                            <span class="currency">元</span>
                                        </div>
                                        <div
                                            v-if="userInfo && service.vip_price && service.currentPrice !== parseFloat(service.vip_price)"
                                            class="original-price">
                                            原会员价: ¥{{ service.vip_price }}
                                        </div>
                                        <div v-else-if="!userInfo && service.currentPrice !== parseFloat(service.price)"
                                             class="original-price">
                                            原价: ¥{{ service.price }}
                                        </div>
                                    </div>

                                    <div class="quantity-section">
                                        <label class="field-label">数量</label>
                                        <el-input-number
                                            v-model="service.quantity"
                                            :min="1"
                                            size="small"
                                            class="quantity-input"
                                            @change="val => changeServiceQuantity(index, val)"
                                        ></el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 已选商品 -->
                        <div class="cart-goods" v-if="selectedGoods.length > 0">
                            <div class="cart-title">商品</div>
                            <div v-for="(goods, index) in selectedGoods" :key="`goods-${index}`" class="cart-item">
                                <div class="item-header">
                                    <div class="item-name-with-image">
                                        <el-image
                                            :src="getImageUrl(goods.image)"
                                            class="goods-thumbnail"
                                            fit="cover"
                                        >
                                            <div slot="error" class="image-slot">
                                                <i class="el-icon-picture-outline"></i>
                                            </div>
                                        </el-image>
                                        <span class="item-name">{{ goods.name }}</span>
                                    </div>
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        icon="el-icon-delete"
                                        circle
                                        @click="removeGoods(index)"
                                        class="delete-btn"
                                    ></el-button>
                                </div>

                                <div class="item-details">
                                    <div class="price-section">
                                        <label class="field-label">价格</label>
                                        <div class="price-input-wrapper">
                                            <el-input-number
                                                v-model="goods.currentPrice"
                                                :min="0"
                                                :precision="2"
                                                size="small"
                                                :controls="false"
                                                class="price-input"
                                                @change="val => changeGoodsPrice(index, val)"
                                            ></el-input-number>
                                            <span class="currency">元</span>
                                        </div>
                                        <div v-if="goods.currentPrice !== parseFloat(goods.sell_price)"
                                             class="original-price">
                                            原价: ¥{{ goods.sell_price }}
                                        </div>
                                    </div>

                                    <div class="quantity-section">
                                        <label class="field-label">数量</label>
                                        <el-input-number
                                            v-model="goods.quantity"
                                            :min="1"
                                            size="small"
                                            class="quantity-input"
                                            @change="val => changeGoodsQuantity(index, val)"
                                        ></el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空购物车提示 -->
                        <div class="empty-cart" v-if="selectedServices.length === 0 && selectedGoods.length === 0">
                            <i class="el-icon-shopping-cart-2"></i>
                            <p>请添加服务或商品</p>
                        </div>
                    </div>
                </div>

                <!-- 理发师和洗发师选择按钮 -->
                <div class="panel-section">
                    <div class="selection-buttons">
                        <div class="selection-item">
                            <span class="selection-label">理发师选择</span>
                            <el-button
                                type="primary"
                                @click="openBarberDialog"
                                :class="{ 'selected': selectedBarberId }"
                            >
                                {{ selectedBarberName || '点击选择' }}
                            </el-button>
                        </div>
                        <div class="selection-item">
                            <span class="selection-label">洗发师选择</span>
                            <el-button
                                type="primary"
                                @click="openHairWashBarberDialog"
                                :class="{ 'selected': selectedHairWashBarberId }"
                            >
                                {{ hairWashBarberName || '点击选择' }}
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 服务项目 -->
                <!--                <div class="panel-section" v-if="selectedBarberId && serviceList.length > 0">-->
                <!--                    <div class="section-title">服务项目</div>-->
                <!--                    <div class="service-list" v-loading="loading.service">-->
                <!--                        <div-->
                <!--                            v-for="service in serviceList"-->
                <!--                            :key="service.id"-->
                <!--                            class="service-item"-->
                <!--                            @click="addService(service)"-->
                <!--                        >-->
                <!--                            <div class="service-info">-->
                <!--                                <div class="service-name">{{ service.title }}</div>-->
                <!--                                <div class="service-prices">-->
                <!--                                    <span class="price">¥{{ service.price }}</span>-->
                <!--                                    <span v-if="service.vip_price" class="vip-price">会员价: ¥{{ service.vip_price }}</span>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                            <el-button type="primary" size="mini">添加</el-button>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>

            <!-- 右侧：商品展示区域 -->
            <div class="right-panel">
                <div class="panel-section goods-section">
                    <div class="section-title">
                        <span>全部</span>
                        <el-input
                            v-model="searchForm.goodsName"
                            placeholder="搜索商品名称"
                            size="small"
                            style="width: 200px"
                            clearable
                            @input="searchGoods"
                        >
                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                    </div>

                    <div class="goods-grid" v-loading="loading.allGoods">
                        <div
                            v-for="(goods, index) in allGoodsList"
                            :key="`goods-item-${goods.id}-${index}`"
                            class="goods-item"
                            @click="addGoods(goods)"
                        >
                            <div class="goods-image">
                                <el-image
                                    :src="getImageUrl(goods.image)"
                                    fit="cover"
                                    style="width: 100%; height: 100%"
                                >
                                    <div slot="error" class="image-slot">
                                        <i class="el-icon-picture-outline"></i>
                                    </div>
                                </el-image>
                            </div>
                            <div class="goods-info">
                                <div class="goods-name">{{ goods.title }}</div>
                                <div class="goods-price">
                                    <!-- 如果有会员且商品有会员价格，显示会员价 -->
                                    <span
                                        v-if="userInfo && userInfo.vip_id && goods.member_price && goods.is_member_price"
                                        class="member-price">¥{{ goods.member_price }}</span>
                                    <!-- 否则显示当前价格或原价 -->
                                    <span v-else>¥{{ goods.current_price || goods.original_price }}</span>
                                    <!-- 如果显示会员价且原价不同，显示划线原价 -->
                                    <span
                                        v-if="userInfo && userInfo.vip_id && goods.member_price && goods.is_member_price && goods.original_price !== goods.member_price"
                                        class="original-price">¥{{ goods.original_price }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部结算区域 -->
        <div class="bottom-settlement">
            <div class="settlement-info">
                <div class="payment-methods">
                    <div class="payment-methods-label">支付方式：</div>
                    <div class="payment-options-grid">
                        <div
                            v-for="(option, index) in paymentOptions"
                            :key="`payment-${option.value}-${index}`"
                            class="payment-option"
                            :class="{
                                'active': paymentMethod === option.value,
                                'disabled': (option.value === 'memberCard' && (!userInfo || !userCards.some(card => card.type === 1))) || (option.value === 'countCard' && (!userInfo || !userCards.some(card => card.type === 2)))
                            }"
                            @click="selectPaymentMethod(option)"
                        >
                            <div class="payment-icon" :style="{ backgroundColor: option.color }">
                                {{ option.icon }}
                            </div>
                            <div class="payment-label">{{ option.label }}</div>
                        </div>
                    </div>
                </div>

                <!-- 会员卡选择 -->
                <div class="card-selection"
                     v-if="(paymentMethod === 'memberCard' || paymentMethod === 'countCard') && userInfo">
                    <span class="card-label">选择{{ paymentMethod === 'memberCard' ? '会员卡余额' : '会员卡次数' }}：</span>
                    <el-select
                        v-model="selectedCardId"
                        placeholder="请选择卡片"
                        size="small"
                        style="width: 150px"
                    >
                        <el-option
                            v-for="(card, index) in availableCards"
                            :key="`available-card-${card.id}-${index}`"
                            :label="cardDisplayText(card)"
                            :value="card.id"
                        ></el-option>
                    </el-select>
                </div>

                <div class="amount-info">
                    <div class="amount-header">
                        <span class="amount-label">应付金额：</span>
                        <div class="amount-mode-switch">
                            <el-radio-group v-model="settleMethod" size="mini">
                                <el-radio-button label="auto">自动计算</el-radio-button>
                                <el-radio-button label="manual">手动输入</el-radio-button>
                            </el-radio-group>
                        </div>
                    </div>

                    <div class="amount-display">
                        <!-- 自动计算模式 -->
                        <div v-if="settleMethod === 'auto'" class="auto-amount">
                            <span class="amount-value">¥{{ userInfo ? vipTotalAmount : totalAmount }}</span>
                            <div v-if="userInfo && vipTotalAmount !== totalAmount" class="discount-info">
                                <small>原价: ¥{{ totalAmount }}</small>
                            </div>
                        </div>

                        <!-- 手动输入模式 -->
                        <div v-else class="manual-amount">
                            <div class="manual-input-wrapper">
                                <span class="currency-symbol">¥</span>
                                <el-input-number
                                    v-model="manualAmount"
                                    :min="0"
                                    :precision="2"
                                    size="large"
                                    :controls="false"
                                    placeholder="输入金额"
                                    class="manual-amount-input"
                                ></el-input-number>
                            </div>

                            <!-- 快捷折扣按钮 -->
                            <div class="discount-shortcuts">
                                <el-button
                                    size="mini"
                                    @click="applyDiscount(0.9)"
                                    class="discount-btn"
                                >9折
                                </el-button>
                                <el-button
                                    size="mini"
                                    @click="applyDiscount(0.8)"
                                    class="discount-btn"
                                >8折
                                </el-button>
                                <el-button
                                    size="mini"
                                    @click="applyDiscount(0.5)"
                                    class="discount-btn"
                                >5折
                                </el-button>
                                <el-button
                                    size="mini"
                                    @click="resetToCalculated"
                                    class="reset-btn"
                                >重置
                                </el-button>
                            </div>

                            <div class="reference-amount">
                                <small>参考金额: ¥{{ userInfo ? vipTotalAmount : totalAmount }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="settlement-actions">
                <el-button size="large" @click="resetForm" :disabled="paymentLoading">清空</el-button>
                <el-button
                    type="primary"
                    size="large"
                    @click="submitOrder"
                    :loading="paymentLoading"
                    :disabled="paymentLoading"
                >
                    {{ paymentLoading ? '支付中...' : `立即结算 ¥${actualAmount}` }}
                </el-button>
            </div>
        </div>

        <!-- 会员查询弹框 -->
        <el-dialog
            title="会员查询"
            :visible.sync="memberDialogVisible"
            width="600px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            center
            class="member-search-dialog"
        >
            <div class="member-dialog-content">
                <div class="search-section">
                    <div class="search-input-group">
                        <el-input
                            v-model="memberSearchForm.mobile"
                            placeholder="请输入手机号或会员编码"
                            clearable
                            size="large"
                            @keyup.enter.native="searchMemberInDialog"
                            class="member-search-input"
                        >
                            <i slot="prefix" class="el-input__icon el-icon-mobile-phone"></i>
                        </el-input>
                        <el-input
                            v-model="memberSearchForm.nickname"
                            placeholder="请输入用户昵称"
                            clearable
                            size="large"
                            @keyup.enter.native="searchMemberInDialog"
                            class="member-search-input"
                        >
                            <i slot="prefix" class="el-input__icon el-icon-user"></i>
                        </el-input>
                        <div class="button-group">
                            <el-button
                                type="primary"
                                size="large"
                                @click="searchMemberInDialog"
                                :loading="loading.user"
                                class="search-btn"
                            >
                                查询
                            </el-button>
                            <el-button
                                size="large"
                                @click="memberSearchForm.mobile = ''; memberSearchForm.nickname = ''"
                                class="reset-btn"
                            >
                                重置
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果显示区域 -->
                <div class="search-results" v-if="memberSearchResults.length > 0">
                    <div class="results-title">搜索结果</div>
                    <div class="member-list">
                        <div
                            v-for="(member, index) in memberSearchResults"
                            :key="`member-${member.id || member.uid || member.user_id}-${index}`"
                            class="member-result-item"
                            @click="selectMemberFromResults(member)"
                        >
                            <div class="member-avatar" :class="getAvatarLevelClass(member)">
                                <div class="avatar-crown" v-if="member && member.level_info">
                                    <i class="crown-icon" :class="getCrownClass(member.level_info)"></i>
                                </div>
                                <el-image
                                    v-if="member.avatar"
                                    :src="member.avatar"
                                    fit="cover"
                                    class="avatar-image"
                                >
                                    <div slot="error" class="avatar-fallback">
                                        <i class="el-icon-user-solid"></i>
                                    </div>
                                </el-image>
                                <i v-else class="el-icon-user-solid"></i>
                            </div>
                            <div class="member-info">
                                <div class="member-name">
                                    {{ member.nickname || member.mobile }}
                                    <span v-if="member.level_info"
                                          class="member-level-badge"
                                          :class="getLevelBadgeClass(member.level_info)">
                                        {{ member.level_info.level_name }}
                                    </span>
                                </div>
                                <div class="member-details">
                                    <span v-if="member.nickname && member.mobile">{{ member.mobile }} | </span>
                                    积分: {{ member.points }}
                                    <span v-if="member.level_info"> | {{ getLevelDiscount(member.level_info) }}折</span>
                                </div>
                            </div>
                            <div class="select-btn">
                                <i class="el-icon-check"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <!--                    <el-button @click="addNewMember" class="add-member-btn">-->
                    <!--                        <i class="el-icon-plus"></i>-->
                    <!--                        添加新会员-->
                    <!--                    </el-button>-->
                    <el-button @click="clearMember" v-if="userInfo" class="clear-member-btn">
                        <i class="el-icon-delete"></i>
                        清除会员
                    </el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 理发师选择弹框 -->
        <el-dialog
            title="理发师选择"
            :visible.sync="barberDialogVisible"
            width="600px"
            :close-on-click-modal="false"
            center
        >
            <div class="barber-dialog-content">
                <div class="search-section">
                    <el-input
                        v-model="barberDialogSearchForm.name"
                        placeholder="搜索理发师姓名，留空显示所有理发师"
                        clearable
                        @input="searchBarberInDialog"
                        size="medium"
                    >
                        <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <div class="search-tip" v-if="!barberDialogSearchForm.name.trim()">
                        <i class="el-icon-info"></i>
                        显示所有可用理发师，点击选择或输入姓名搜索
                    </div>
                </div>

                <div class="barber-list" v-loading="loading.barber">
                    <div
                        v-for="(barber, index) in barberList"
                        :key="`barber-${barber.id}-${index}`"
                        class="barber-item"
                        :class="{ 'selected': selectedBarberId === barber.id }"
                        @click="selectBarberInDialog(barber)"
                    >
                        <div class="barber-info">
                            <div class="barber-name">{{ barber.name }}</div>
                            <div class="barber-details">{{ barber.shop_name }} / {{ barber.job_name }}</div>
                        </div>
                        <div class="select-indicator" v-if="selectedBarberId === barber.id">
                            <i class="el-icon-check"></i>
                        </div>
                    </div>
                    <div v-if="barberList.length === 0 && !loading.barber" class="no-data">
                        <i class="el-icon-warning-outline"></i>
                        <span v-if="barberDialogSearchForm.name.trim()">未找到匹配的理发师</span>
                        <span v-else>暂无可用理发师</span>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 洗发师选择弹框 -->
        <el-dialog
            title="洗发师选择"
            :visible.sync="hairWashBarberDialogVisible"
            width="600px"
            :close-on-click-modal="false"
            center
        >
            <div class="barber-dialog-content">
                <div class="search-section">
                    <el-input
                        v-model="hairWashBarberDialogSearchForm.name"
                        placeholder="搜索洗发师姓名，留空显示所有洗发师"
                        clearable
                        @input="searchHairWashBarberInDialog"
                        size="medium"
                    >
                        <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <div class="search-tip" v-if="!hairWashBarberDialogSearchForm.name.trim()">
                        <i class="el-icon-info"></i>
                        显示所有可用洗发师，点击选择或输入姓名搜索
                    </div>
                </div>

                <div class="barber-list" v-loading="loading.hairWashBarber">
                    <div
                        v-for="(barber, index) in hairWashBarberList"
                        :key="`hair-wash-barber-${barber.id}-${index}`"
                        class="barber-item"
                        :class="{ 'selected': selectedHairWashBarberId === barber.id }"
                        @click="selectHairWashBarberInDialog(barber)"
                    >
                        <div class="barber-info">
                            <div class="barber-name">{{ barber.name }}</div>
                            <div class="barber-details">{{ barber.shop_name }} / {{ barber.job_name }}</div>
                        </div>
                        <div class="select-indicator" v-if="selectedHairWashBarberId === barber.id">
                            <i class="el-icon-check"></i>
                        </div>
                    </div>
                    <div v-if="hairWashBarberList.length === 0 && !loading.hairWashBarber" class="no-data">
                        <i class="el-icon-warning-outline"></i>
                        <span v-if="hairWashBarberDialogSearchForm.name.trim()">未找到匹配的洗发师</span>
                        <span v-else>暂无可用洗发师</span>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 水单弹框 -->
        <slip-dialog
            v-model="slipDialogVisible"
            :slip-data="slipData"
            :statistics="slipStatistics"
            :show-print-button="true"
            @close="handleSlipDialogClose"
            @print="handleSlipPrint"
        />
    </div>
</template>

<style lang="scss" scoped>
.cashier-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;

    .cashier-content {
        display: flex;
        gap: 20px;
        flex: 1;
        //overflow: hidden;
        padding: 20px;


        .left-panel {
            width: 350px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: calc(100vh - 200px); // 增加底部结算区域的高度预留
            overflow-y: auto;
            padding-right: 5px; // 为滚动条留出空间

            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;

                &:hover {
                    background: #a8a8a8;
                }
            }
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            height: calc(100vh - 200px); // 与左侧面板保持一致的高度
        }

        .panel-section {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .section-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .section-subtitle {
                font-size: 14px;
                font-weight: bold;
                margin: 10px 0;
            }
        }

        .cart-section {
            display: flex;
            flex-direction: column;
            height: 350px; // 减少高度，为底部结算区域留出空间
            min-height: 350px;
            max-height: 350px;
        }

        .cart-container {
            flex: 1;
            overflow-y: auto;
            padding-right: 5px;
            height: calc(100% - 40px); // 减去标题高度

            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: #f8f8f8;
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: #d1d1d1;
                border-radius: 3px;

                &:hover {
                    background: #b8b8b8;
                }
            }
        }

        .goods-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            max-height: calc(100vh - 240px); // 限制最大高度，为底部结算区域留出空间
        }

        // 商品网格布局
        .goods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            padding: 15px 0;
            flex: 1;
            overflow-y: auto;

            .goods-item {
                background: #fff;
                border-radius: 8px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 1px solid #e8e8e8;
                height: 170px;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                .goods-image {
                    width: 100%;
                    height: 100px;
                    border-radius: 6px;
                    overflow: hidden;
                    margin-bottom: 8px;

                    .image-slot {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #f5f5f5;
                        color: #ccc;
                        font-size: 24px;
                    }
                }

                .goods-info {
                    .goods-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 4px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .goods-price {
                        font-size: 16px;
                        font-weight: bold;
                        color: #ff4757;

                        .member-price {
                            color: #ff4757;
                            font-weight: bold;
                        }

                        .original-price {
                            font-size: 12px;
                            color: #999;
                            text-decoration: line-through;
                            margin-left: 5px;
                            font-weight: normal;
                        }
                    }
                }
            }
        }

        // 选择按钮样式
        .selection-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;

            .selection-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .selection-label {
                    font-weight: 500;
                    color: #333;
                }

                .el-button {
                    flex: 1;
                    margin-left: 15px;

                    &.selected {
                        background-color: #67c23a;
                        border-color: #67c23a;
                    }
                }
            }
        }

        // 服务项目样式
        .service-list {
            .service-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                margin-bottom: 8px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #409eff;
                    background-color: #f0f9ff;
                }

                .service-info {
                    flex: 1;

                    .service-name {
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 4px;
                    }

                    .service-prices {
                        .price {
                            color: #ff4757;
                            font-weight: bold;
                            margin-right: 10px;
                        }

                        .vip-price {
                            color: #67c23a;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        // 紧凑型客户信息卡片样式
        .customer-section {
            padding: 15px !important;
        }

        .compact-customer-card {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #409eff;
            border-radius: 8px;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;

            &:hover {
                background-color: #f0f9ff;
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(64, 158, 255, 0.2);
            }

            .customer-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background-color: #e1f5fe;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                overflow: visible;
                position: relative;
                border: 2px solid transparent;
                transition: all 0.3s ease;

                .avatar-image {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }

                .avatar-fallback {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #e1f5fe;
                    border-radius: 50%;

                    i {
                        font-size: 20px;
                        color: #409eff;
                    }
                }

                i {
                    font-size: 20px;
                    color: #409eff;
                }

                // 皇冠装饰
                .avatar-crown {
                    position: absolute;
                    top: -8px;
                    right: -5px;
                    z-index: 10;

                    .crown-icon {
                        font-size: 16px;
                        display: block;

                        &::before {
                            content: '♔';
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                        }
                    }
                }

                // 等级边框样式 - 动态主题
                &.avatar-bronze {
                    border-color: #CD7F32;
                    box-shadow: 0 0 8px rgba(205, 127, 50, 0.3);
                    .crown-icon::before { color: #CD7F32; }
                }

                &.avatar-silver {
                    border-color: #C0C0C0;
                    box-shadow: 0 0 8px rgba(192, 192, 192, 0.3);
                    .crown-icon::before { color: #C0C0C0; }
                }

                &.avatar-gold {
                    border-color: #FFD700;
                    box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
                    .crown-icon::before { color: #FFD700; }
                }

                &.avatar-platinum {
                    border-color: #E5E4E2;
                    box-shadow: 0 0 8px rgba(229, 228, 226, 0.3);
                    .crown-icon::before { color: #E5E4E2; }
                }

                &.avatar-diamond {
                    border-color: #B9F2FF;
                    box-shadow: 0 0 8px rgba(185, 242, 255, 0.3);
                    .crown-icon::before { color: #B9F2FF; }
                }

                &.avatar-ruby {
                    border-color: #E0115F;
                    box-shadow: 0 0 8px rgba(224, 17, 95, 0.3);
                    .crown-icon::before { color: #E0115F; }
                }

                &.avatar-emerald {
                    border-color: #50C878;
                    box-shadow: 0 0 8px rgba(80, 200, 120, 0.3);
                    .crown-icon::before { color: #50C878; }
                }

                &.avatar-sapphire {
                    border-color: #0F52BA;
                    box-shadow: 0 0 8px rgba(15, 82, 186, 0.3);
                    .crown-icon::before { color: #0F52BA; }
                }

                &.avatar-default {
                    border-color: #909399;
                    box-shadow: 0 0 8px rgba(144, 147, 153, 0.3);
                    .crown-icon::before { color: #909399; }
                }
            }

            .customer-info {
                flex: 1;

                .customer-name {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 2px;
                    display: flex;
                    align-items: center;
                    gap: 6px;

                    .customer-gender {
                        font-size: 12px;
                        padding: 2px 6px;
                        border-radius: 10px;
                        font-weight: normal;

                        &.male {
                            background-color: #f0f9ff;
                            color: #409eff;
                        }

                        &.female {
                            background-color: #fdf2f8;
                            color: #ec4899;
                        }
                    }

                    .member-level-badge {
                        margin-left: 8px;
                        font-size: 11px;
                        padding: 2px 6px;
                        border-radius: 10px;
                        font-weight: bold;
                        text-shadow: 0 1px 1px rgba(0,0,0,0.1);

                        &.badge-bronze {
                            background: linear-gradient(135deg, #CD7F32, #B8860B);
                            color: white;
                        }

                        &.badge-silver {
                            background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
                            color: white;
                        }

                        &.badge-gold {
                            background: linear-gradient(135deg, #FFD700, #FFA500);
                            color: #333;
                        }

                        &.badge-platinum {
                            background: linear-gradient(135deg, #E5E4E2, #D3D3D3);
                            color: #333;
                        }

                        &.badge-diamond {
                            background: linear-gradient(135deg, #B9F2FF, #87CEEB);
                            color: #333;
                        }

                        &.badge-ruby {
                            background: linear-gradient(135deg, #E0115F, #DC143C);
                            color: white;
                        }

                        &.badge-emerald {
                            background: linear-gradient(135deg, #50C878, #228B22);
                            color: white;
                        }

                        &.badge-sapphire {
                            background: linear-gradient(135deg, #0F52BA, #4169E1);
                            color: white;
                        }

                        &.badge-default {
                            background: linear-gradient(135deg, #909399, #606266);
                            color: white;
                        }
                    }
                }

                .customer-status {
                    font-size: 12px;
                    color: #666;
                }
            }

            .customer-action {
                color: #409eff;
                font-size: 16px;
            }
        }

        // 快速会员卡显示
        .quick-cards {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;

            .quick-card-item {
                flex: 1;
                min-width: 80px;
                padding: 8px;
                border-radius: 6px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 12px;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                }

                &.quick-card-member {
                    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
                    color: #fff;
                }

                &.quick-card-count {
                    background: linear-gradient(135deg, #ff9c9c 0%, #ff7875 100%);
                    color: #fff;
                }

                .quick-card-type {
                    font-weight: bold;
                    margin-bottom: 4px;
                }

                .quick-card-value {
                    font-size: 11px;
                    opacity: 0.9;
                }
            }
        }

        // 已选择会员卡片样式
        .selected-member-card {
            .member-header {
                display: flex;
                align-items: center;
                padding: 15px;
                border: 2px solid #67c23a;
                border-radius: 8px;
                background-color: #f0f9ff;
                margin-bottom: 15px;

                .member-avatar {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    background-color: #a3d9a3;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 15px;

                    i {
                        font-size: 24px;
                        color: #67c23a;
                    }
                }

                .member-details {
                    flex: 1;

                    .member-mobile {
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                    }

                    .member-points {
                        color: #67c23a;
                        margin-top: 5px;
                        font-size: 14px;
                    }
                }

                .change-member-btn {
                    color: #409eff;
                    font-size: 14px;
                    cursor: pointer;
                    padding: 6px 12px;
                    border: 1px solid #409eff;
                    border-radius: 4px;
                    transition: all 0.3s ease;

                    &:hover {
                        background-color: #409eff;
                        color: #fff;
                    }
                }
            }
        }

        .user-card {
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;

            .user-mobile {
                font-weight: bold;
                font-size: 16px;
            }

            .user-points {
                color: #67c23a;
                margin-top: 5px;
            }
        }

        .cards-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;

            .card-item {
                min-height: 120px;
                border-radius: 12px;
                padding: 20px;
                color: #fff;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;

                &:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                }

                &.card-member {
                    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
                }

                &.card-count {
                    background: linear-gradient(135deg, #ff9c9c 0%, #ff7875 100%);
                }

                .card-title {
                    font-weight: bold;
                    font-size: 18px;
                    margin-bottom: 15px;
                }

                .card-balance,
                .card-count {
                    font-size: 16px;
                    font-weight: 600;
                    margin-top: auto;
                    opacity: 0.95;
                }
            }
        }

        .cart-title {
            font-weight: bold;
            margin: 8px 0;
            background-color: #f8f8f8;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .cart-item {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 12px;
            margin-bottom: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

            &:hover {
                border-color: #409eff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }

            &:last-child {
                margin-bottom: 0;
            }

            .item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 12px;
                border-bottom: 1px solid #f0f0f0;

                .item-name {
                    font-weight: 600;
                    font-size: 15px;
                    color: #333;
                    flex: 1;
                }

                .item-name-with-image {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 1;

                    .goods-thumbnail {
                        width: 40px;
                        height: 40px;
                        border-radius: 8px;
                        flex-shrink: 0;
                        border: 1px solid #e8e8e8;
                    }

                    .item-name {
                        font-weight: 600;
                        font-size: 15px;
                        color: #333;
                        margin: 0;
                    }
                }

                .delete-btn {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: #fff;
                    border: 1px solid #ff4757;
                    color: #ff4757;
                    transition: all 0.3s ease;

                    &:hover {
                        background: #ff4757;
                        color: #fff;
                        transform: scale(1.1);
                    }
                }
            }

            .item-details {
                display: flex;
                gap: 20px;
                align-items: flex-start;

                .price-section,
                .quantity-section {
                    flex: 1;
                }

                .field-label {
                    display: block;
                    font-size: 13px;
                    color: #666;
                    font-weight: 500;
                    margin-bottom: 8px;
                }

                .price-input-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 4px;

                    .price-input {
                        flex: 1;
                        max-width: 120px;

                        ::v-deep .el-input__inner {
                            border-radius: 8px;
                            border: 2px solid #e8e8e8;
                            font-weight: 600;
                            color: #ff4757;
                            text-align: center;
                            padding: 0 12px;

                            &:focus {
                                border-color: #409eff;
                                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
                            }
                        }
                    }

                    .currency {
                        font-size: 14px;
                        color: #666;
                        font-weight: 500;
                    }
                }

                .quantity-input {
                    max-width: 100px;

                    ::v-deep .el-input__inner {
                        border-radius: 8px;
                        border: 2px solid #e8e8e8;
                        text-align: center;
                        font-weight: 600;

                        &:focus {
                            border-color: #409eff;
                            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
                        }
                    }

                    ::v-deep .el-input-number__increase,
                    ::v-deep .el-input-number__decrease {
                        border: none;
                        background: #f8f9fa;
                        color: #666;

                        &:hover {
                            background: #409eff;
                            color: #fff;
                        }
                    }
                }

                .original-price {
                    font-size: 12px;
                    color: #999;
                    font-style: italic;
                    margin-top: 4px;
                }
            }


            // 商品图片错误状态样式
            .image-slot {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f5f5;
                color: #ccc;
                font-size: 16px;
                border-radius: 8px;
            }
        }

        .empty-cart {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            i {
                font-size: 48px;
                margin-bottom: 12px;
                opacity: 0.6;
            }

            p {
                margin: 0;
                font-size: 14px;
                opacity: 0.8;
            }
        }

        .settlement {
            margin-top: 20px;
            border-top: 1px solid #ebeef5;
            padding-top: 15px;

            .settlement-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;

                .price {
                    font-size: 24px;
                    color: #f56c6c;
                    font-weight: bold;
                }

                .discount-tip {
                    color: #67c23a;
                    margin-left: 10px;
                }

                .manual-price-input {
                    display: flex;
                    align-items: center;

                    .price-symbol {
                        font-size: 24px;
                        margin-right: 5px;
                        color: #f56c6c;
                    }
                }

                &.final-amount {
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px dashed #ebeef5;
                    font-weight: bold;

                    .price {
                        font-size: 28px;
                    }
                }
            }

            .settlement-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 20px;
            }
        }
    }

    // 底部结算区域
    .bottom-settlement {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        padding: 20px 24px;
        border-top: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
        border-radius: 12px 12px 0 0;
        min-height: 160px; // 设置最小高度
        //max-height: 180px; // 设置最大高度，防止过高
        flex-shrink: 0; // 防止被压缩

        .settlement-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
            margin-right: 30px;

            .payment-methods {
                display: flex;
                flex-direction: column;
                gap: 12px;

                .payment-methods-label {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 8px;
                }

                .payment-options-grid {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px; // 减少间距以适应更紧凑的布局

                    .payment-option {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        padding: 8px 12px; // 减少内边距
                        border: 2px solid #e8e8e8;
                        border-radius: 10px; // 稍微减少圆角
                        cursor: pointer;
                        transition: all 0.3s ease;
                        background: #fff;
                        min-width: 70px; // 减少最小宽度
                        position: relative;

                        &:hover:not(.disabled) {
                            border-color: #409eff;
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
                        }

                        &.active {
                            border-color: #409eff;
                            background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 100%);
                            box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);

                            &::after {
                                content: '✓';
                                position: absolute;
                                top: -8px;
                                right: -8px;
                                width: 20px;
                                height: 20px;
                                background: #409eff;
                                color: white;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 12px;
                                font-weight: bold;
                            }

                            .payment-label {
                                color: #409eff;
                                font-weight: 600;
                            }
                        }

                        &.disabled {
                            opacity: 0.5;
                            cursor: not-allowed;
                            background: #f5f5f5;

                            .payment-icon {
                                opacity: 0.6;
                            }

                            .payment-label {
                                color: #999;
                            }
                        }

                        .payment-icon {
                            width: 32px; // 减少图标大小
                            height: 32px;
                            border-radius: 6px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 16px; // 减少字体大小
                            margin-bottom: 6px; // 减少底部间距
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            transition: all 0.3s ease;
                        }

                        .payment-label {
                            font-size: 12px; // 减少字体大小
                            font-weight: 500;
                            color: #333;
                            text-align: center;
                            transition: all 0.3s ease;
                        }
                    }
                }
            }

            .card-selection {
                display: flex;
                align-items: center;
                font-size: 14px;
                background: #f8f9fa;
                padding: 12px 16px;
                border-radius: 8px;
                border: 1px solid #e9ecef;

                .card-label {
                    color: #495057;
                    margin-right: 12px;
                    white-space: nowrap;
                    font-weight: 500;
                }

                .el-select {
                    .el-input__inner {
                        border-radius: 6px;
                        border-color: #ced4da;

                        &:focus {
                            border-color: #409eff;
                            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                        }
                    }
                }
            }

            .amount-info {
                background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
                padding: 20px;
                border-radius: 12px;
                border: 2px solid #ffebee;
                box-shadow: 0 4px 12px rgba(255, 71, 87, 0.1);

                .amount-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;

                    .amount-label {
                        color: #666;
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .amount-mode-switch {
                        ::v-deep .el-radio-group {
                            .el-radio-button__inner {
                                padding: 8px 12px;
                                font-size: 12px;
                                border-radius: 6px;
                                transition: all 0.3s ease;
                            }

                            .el-radio-button:first-child .el-radio-button__inner {
                                border-radius: 6px 0 0 6px;
                            }

                            .el-radio-button:last-child .el-radio-button__inner {
                                border-radius: 0 6px 6px 0;
                            }

                            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
                                background: #409eff;
                                border-color: #409eff;
                                color: #fff;
                                box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
                            }
                        }
                    }
                }

                .amount-display {
                    .auto-amount {
                        text-align: center;

                        .amount-value {
                            color: #ff4757;
                            font-weight: 700;
                            font-size: 32px;
                            text-shadow: 1px 1px 2px rgba(255, 71, 87, 0.1);
                            display: block;
                        }

                        .discount-info {
                            margin-top: 8px;

                            small {
                                color: #999;
                                font-size: 14px;
                                text-decoration: line-through;
                            }
                        }
                    }

                    .manual-amount {
                        .manual-input-wrapper {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                            margin-bottom: 8px;

                            .currency-symbol {
                                font-size: 24px;
                                font-weight: 700;
                                color: #ff4757;
                            }

                            .manual-amount-input {
                                ::v-deep .el-input__inner {
                                    border: 2px solid #409eff;
                                    border-radius: 8px;
                                    font-size: 24px;
                                    font-weight: 700;
                                    color: #ff4757;
                                    text-align: center;
                                    padding: 12px 16px;
                                    width: 180px;

                                    &:focus {
                                        border-color: #409eff;
                                        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
                                    }

                                    &::placeholder {
                                        color: #c0c4cc;
                                        font-weight: 400;
                                    }
                                }
                            }
                        }

                        .discount-shortcuts {
                            display: flex;
                            justify-content: center;
                            gap: 8px;
                            margin: 12px 0;

                            .discount-btn {
                                background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
                                border: none;
                                color: #fff;
                                border-radius: 6px;
                                padding: 6px 12px;
                                font-size: 12px;
                                font-weight: 600;
                                transition: all 0.3s ease;

                                &:hover {
                                    background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
                                    transform: translateY(-1px);
                                    box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
                                }
                            }

                            .reset-btn {
                                background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
                                border: none;
                                color: #fff;
                                border-radius: 6px;
                                padding: 6px 12px;
                                font-size: 12px;
                                font-weight: 600;
                                transition: all 0.3s ease;

                                &:hover {
                                    background: linear-gradient(135deg, #a6a9ad 0%, #b1b3b8 100%);
                                    transform: translateY(-1px);
                                    box-shadow: 0 4px 8px rgba(144, 147, 153, 0.3);
                                }
                            }
                        }

                        .reference-amount {
                            text-align: center;

                            small {
                                color: #999;
                                font-size: 13px;
                                font-style: italic;
                            }
                        }
                    }
                }
            }
        }

        .settlement-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
            min-width: 200px;

            .el-button {
                padding: 16px 32px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 10px;
                transition: all 0.3s ease;

                &:first-child {
                    background: #f8f9fa;
                    border-color: #dee2e6;
                    color: #6c757d;

                    &:hover {
                        background: #e9ecef;
                        border-color: #adb5bd;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                }

                &.el-button--primary {
                    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
                    border: none;
                    box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);

                    &:hover {
                        background: linear-gradient(135deg, #36a3f7 0%, #2b8ce6 100%);
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
                    }

                    &:active {
                        transform: translateY(0);
                    }
                }
            }
        }
    }
}

// 理发师弹框样式
.barber-dialog-content {
    .el-input {
        margin-bottom: 20px;
    }

    .barber-list {
        max-height: 400px;
        overflow-y: auto;

        .barber-item {
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: #409eff;
                background-color: #f0f9ff;
            }

            .barber-info {
                .barber-name {
                    font-weight: bold;
                    font-size: 16px;
                    color: #333;
                    margin-bottom: 5px;
                }

                .barber-details {
                    color: #666;
                    font-size: 14px;
                }
            }
        }

        .no-data {
            text-align: center;
            padding: 30px;
            color: #999;
        }
    }
}

.selected-hair-wash-barber {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;

    .section-subtitle {
        margin-bottom: 10px;
        font-weight: bold;
    }

    .el-tag {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        margin-right: 10px;
        margin-bottom: 10px;

        .el-icon-close {
            margin-left: 8px;
            cursor: pointer;

            &:hover {
                color: #f56c6c;
            }
        }
    }
}

// 会员查询弹框样式
.member-search-dialog {
    ::v-deep .el-dialog__body {
        padding: 20px 30px 30px;
    }
}

.member-dialog-content {
    .search-section {
        margin-bottom: 20px;

        .search-input-group {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .member-search-input {
                width: 100%;
            }

            .button-group {
                display: flex;
                gap: 8px;
                justify-content: flex-end;

                .search-btn {
                    min-width: 80px;
                }

                .reset-btn {
                    min-width: 60px;
                }
            }
        }
    }

    .search-results {
        margin: 20px 0;
        max-height: 300px;
        overflow-y: auto;

        .results-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8e8e8;
        }

        .member-list {
            .member-result-item {
                display: flex;
                align-items: center;
                padding: 12px;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                margin-bottom: 8px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #409eff;
                    background-color: #f0f9ff;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
                }

                .member-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background-color: #e1f5fe;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;
                    overflow: hidden;

                    .avatar-image {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }

                    .avatar-fallback {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #e1f5fe;
                        border-radius: 50%;

                        i {
                            font-size: 18px;
                            color: #409eff;
                        }
                    }

                    i {
                        font-size: 18px;
                        color: #409eff;
                    }
                }

                .member-info {
                    flex: 1;

                    .member-name {
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 4px;
                        display: flex;
                        align-items: center;
                        gap: 6px;

                        .member-gender {
                            font-size: 12px;
                            padding: 2px 6px;
                            border-radius: 10px;
                            font-weight: normal;

                            &.male {
                                background-color: #f0f9ff;
                                color: #409eff;
                            }

                            &.female {
                                background-color: #fdf2f8;
                                color: #ec4899;
                            }
                        }
                    }

                    .member-details {
                        font-size: 12px;
                        color: #67c23a;
                    }

                    // 保持向后兼容
                    .member-mobile {
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 4px;
                    }

                    .member-points {
                        font-size: 12px;
                        color: #67c23a;
                    }
                }

                .select-btn {
                    color: #409eff;
                    font-size: 18px;
                    opacity: 0.7;
                    transition: opacity 0.3s ease;
                }

                &:hover .select-btn {
                    opacity: 1;
                }
            }
        }
    }

    .quick-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e8e8e8;

        .el-button {
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .add-member-btn {
            background-color: #67c23a;
            border-color: #67c23a;
            color: #fff;

            &:hover {
                background-color: #5daf34;
                border-color: #5daf34;
            }
        }

        .clear-member-btn {
            background-color: #f56c6c;
            border-color: #f56c6c;
            color: #fff;

            &:hover {
                background-color: #f78989;
                border-color: #f78989;
            }
        }
    }
}

// 弹框标题样式调整
::v-deep .el-dialog__header {
    text-align: center;
    padding: 20px 20px 0;

    .el-dialog__title {
        font-size: 18px;
        font-weight: bold;
    }
}

// 弹框关闭按钮样式
::v-deep .el-dialog__headerbtn {
    top: 15px;
    right: 15px;

    .el-dialog__close {
        font-size: 20px;
        color: #909399;

        &:hover {
            color: #f56c6c;
        }
    }
}

// 会员搜索结果样式
.search-results {
    margin-top: 20px;

    .results-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }

    .member-list {
        max-height: 300px;
        overflow-y: auto;

        .member-result-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                background-color: #f0f9ff;
                border-color: #409eff;
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(64, 158, 255, 0.2);
            }

            .member-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background-color: #e1f5fe;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 15px;
                overflow: visible;
                position: relative;
                border: 2px solid transparent;
                transition: all 0.3s ease;

                .avatar-image {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }

                .avatar-fallback {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #e1f5fe;
                    border-radius: 50%;

                    i {
                        font-size: 20px;
                        color: #409eff;
                    }
                }

                i {
                    font-size: 20px;
                    color: #409eff;
                }

                // 皇冠装饰
                .avatar-crown {
                    position: absolute;
                    top: -8px;
                    right: -5px;
                    z-index: 10;

                    .crown-icon {
                        font-size: 16px;
                        display: block;

                        &::before {
                            content: '♔';
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                        }
                    }
                }

                // 等级边框样式 - 动态主题
                &.avatar-bronze {
                    border-color: #CD7F32;
                    box-shadow: 0 0 8px rgba(205, 127, 50, 0.3);
                    .crown-icon::before { color: #CD7F32; }
                }

                &.avatar-silver {
                    border-color: #C0C0C0;
                    box-shadow: 0 0 8px rgba(192, 192, 192, 0.3);
                    .crown-icon::before { color: #C0C0C0; }
                }

                &.avatar-gold {
                    border-color: #FFD700;
                    box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
                    .crown-icon::before { color: #FFD700; }
                }

                &.avatar-platinum {
                    border-color: #E5E4E2;
                    box-shadow: 0 0 8px rgba(229, 228, 226, 0.3);
                    .crown-icon::before { color: #E5E4E2; }
                }

                &.avatar-diamond {
                    border-color: #B9F2FF;
                    box-shadow: 0 0 8px rgba(185, 242, 255, 0.3);
                    .crown-icon::before { color: #B9F2FF; }
                }

                &.avatar-ruby {
                    border-color: #E0115F;
                    box-shadow: 0 0 8px rgba(224, 17, 95, 0.3);
                    .crown-icon::before { color: #E0115F; }
                }

                &.avatar-emerald {
                    border-color: #50C878;
                    box-shadow: 0 0 8px rgba(80, 200, 120, 0.3);
                    .crown-icon::before { color: #50C878; }
                }

                &.avatar-sapphire {
                    border-color: #0F52BA;
                    box-shadow: 0 0 8px rgba(15, 82, 186, 0.3);
                    .crown-icon::before { color: #0F52BA; }
                }

                &.avatar-default {
                    border-color: #909399;
                    box-shadow: 0 0 8px rgba(144, 147, 153, 0.3);
                    .crown-icon::before { color: #909399; }
                }
            }

            .member-info {
                flex: 1;

                .member-name {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 5px;
                    display: flex;
                    align-items: center;

                    .member-level-badge {
                        margin-left: 8px;
                        font-size: 11px;
                        padding: 2px 6px;
                        border-radius: 10px;
                        font-weight: bold;
                        text-shadow: 0 1px 1px rgba(0,0,0,0.1);

                        &.badge-bronze {
                            background: linear-gradient(135deg, #CD7F32, #B8860B);
                            color: white;
                        }

                        &.badge-silver {
                            background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
                            color: white;
                        }

                        &.badge-gold {
                            background: linear-gradient(135deg, #FFD700, #FFA500);
                            color: #333;
                        }

                        &.badge-platinum {
                            background: linear-gradient(135deg, #E5E4E2, #D3D3D3);
                            color: #333;
                        }

                        &.badge-diamond {
                            background: linear-gradient(135deg, #B9F2FF, #87CEEB);
                            color: #333;
                        }

                        &.badge-ruby {
                            background: linear-gradient(135deg, #E0115F, #DC143C);
                            color: white;
                        }

                        &.badge-emerald {
                            background: linear-gradient(135deg, #50C878, #228B22);
                            color: white;
                        }

                        &.badge-sapphire {
                            background: linear-gradient(135deg, #0F52BA, #4169E1);
                            color: white;
                        }

                        &.badge-default {
                            background: linear-gradient(135deg, #909399, #606266);
                            color: white;
                        }
                    }
                }

                .member-details {
                    font-size: 12px;
                    color: #666;
                }
            }

            .select-btn {
                color: #409eff;
                font-size: 18px;
                margin-left: 10px;
            }
        }
    }
}

// 理发师选择弹框样式
.barber-dialog-content {
    .search-section {
        margin-bottom: 16px;

        .search-tip {
            margin-top: 8px;
            padding: 8px 12px;
            background-color: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            font-size: 13px;
            color: #409eff;
            display: flex;
            align-items: center;

            i {
                margin-right: 6px;
            }
        }
    }

    .barber-list {
        max-height: 400px;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        padding: 4px;

        .barber-item {
            padding: 16px 12px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            min-height: 100px;
            background: #fff;

            &:hover {
                border-color: #409eff;
                background-color: #f0f9ff;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }

            &.selected {
                border-color: #67c23a;
                background-color: #f0f9ff;
                box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
                transform: translateY(-2px);
            }

            .barber-info {
                width: 100%;

                .barber-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 8px;
                    line-height: 1.4;
                    word-break: break-all;
                }

                .barber-details {
                    font-size: 12px;
                    color: #909399;
                    line-height: 1.3;
                    word-break: break-all;
                }
            }

            .select-indicator {
                position: absolute;
                top: 8px;
                right: 8px;
                color: #67c23a;
                font-size: 16px;
                font-weight: bold;
                background: #fff;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
        }

        .no-data {
            grid-column: 1 / -1;
            text-align: center;
            color: #909399;
            padding: 40px 0;
            font-size: 14px;
            display: flex;
            flex-direction: column;
            align-items: center;

            i {
                font-size: 24px;
                margin-bottom: 8px;
            }
        }

        // 响应式设计
        @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        @media (max-width: 480px) {
            grid-template-columns: 1fr;
            gap: 8px;
        }
    }
}
</style>
