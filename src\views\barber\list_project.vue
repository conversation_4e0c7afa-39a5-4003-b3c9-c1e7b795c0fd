<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
  apiProjectSave,
  apiProjectDel,
  apiCreateProjectList,
  apiProjectDetail
} from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'
import config from '@/config'

@Component({
  name: 'ProjectList',
  components: {
    LsPagination
  }
})
export default class ProjectList extends Vue {
  // 分页数据
  private pager = new RequestPaging()

  // 项目弹窗
  private projectDialog = {
    visible: false,
    title: '新增项目',
    isEdit: false
  }

  // 项目表单
  private projectForm = {
    id: '',
    title: '',
    desc: '',
    price: '',
    image: '',
    vip_price: ''
  }

  // 项目表单规则
  private projectRules = {
    title: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    desc: [{ required: true, message: '请输入项目描述', trigger: 'blur' }],
    price: [
      { required: true, message: '请输入项目价格', trigger: 'blur' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
    ],
    vip_price: [
      { required: true, message: '请输入会员价格', trigger: 'blur' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
    ]
  }

  // 搜索关键词
  private searchKeyword = ''

  // 加载状态
  private loading = false

  // 用于备份项目列表数据，避免pager.lists为空时的错误
  private projectLists: any[] = []

  // 图片上传相关
  private uploadAction = `${config.baseURL}/adminapi/upload/image`
  private uploadHeaders = {
    token: this.$store.getters.token,
    version: config.version
  }

  created() {
    this.getProjectList()
  }

  // 获取项目列表
  async getProjectList() {

    this.loading = true
    await apiCreateProjectList({
        title: this.searchKeyword
    }).then((res: any) => {
        // console.log('res',res);

        this.projectLists = res
    }).catch((err: any) => {
        console.error('获取项目列表失败', err)
    })
.finally(() => {
        this.loading = false
    })
  }

  // 打开新增项目弹窗
  handleAddProject() {
    this.projectDialog = {
      visible: true,
      title: '新增项目',
      isEdit: false
    }
    this.projectForm = {
      id: '',
      title: '',
      desc: '',
      price: '',
      image: '',
      vip_price: ''
    }
  }

  // 打开编辑项目弹窗
  async handleEditProject(project: any) {
    this.projectDialog = {
      visible: true,
      title: '编辑项目',
      isEdit: true
    }

    try {
      this.loading = true
      const res = await apiProjectDetail({ id: project.id })
      if (res) {
        this.projectForm = {
          id: res.id,
          title: res.title || '',
          desc: res.desc || '',
          price: res.price || '',
          image: res.image || '',
          vip_price: res.vip_price || ''
        }
      }
    } catch (error) {
      console.error('获取项目详情失败', error)
    } finally {
      this.loading = false
    }
  }

  // 删除项目
  async handleDeleteProject(project: any) {
    try {
      await this.$confirm('确认删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      this.loading = true
      try {
        await apiProjectDel({ id: project.id })
        this.$message.success('删除成功')
        // 如果删除后当前页没有数据了，则回到上一页
        if (this.projectLists.length === 1 && this.pager.page > 1) {
          this.pager.page--
        }
        this.getProjectList()
      } catch (error) {
        console.error('删除项目失败', error)
        this.$message.error('删除失败')
      }
    } catch (error) {
      console.log('取消删除')
    } finally {
      this.loading = false
    }
  }

  // 保存项目
  async handleSaveProject() {
    const formRef = this.$refs.projectForm as any
    if (!formRef) {
      return
    }

    try {
      await formRef.validate()

      this.loading = true
      try {
        const params = {
          ...this.projectForm
        }

        await apiProjectSave(params)
        this.$message.success(this.projectDialog.isEdit ? '修改成功' : '新增成功')
        this.projectDialog.visible = false
        this.getProjectList()
      } catch (error) {
        console.error('保存项目失败', error)
        this.$message.error(this.projectDialog.isEdit ? '修改失败' : '新增失败')
      }
    } catch (error) {
      console.error('表单验证失败', error)
    } finally {
      this.loading = false
    }
  }

  // 搜索
  handleSearch() {
    this.pager.page = 1
    this.getProjectList()
  }

  // 重置搜索
  resetSearch() {
    this.searchKeyword = ''
    this.handleSearch()
  }

  // 关闭弹窗
  handleCloseDialog() {
    this.projectDialog.visible = false
  }

  // 图片上传成功
  handleImageSuccess(response: any, file: any) {
    if (response.code === 1) {
      this.projectForm.image = response.data.url
      this.$message.success('图片上传成功')
    } else {
      this.$message.error(response.msg || '图片上传失败')
    }
  }

  // 图片上传失败
  handleImageError(error: any) {
    this.$message.error('图片上传失败')
    console.error('图片上传失败', error)
  }

  // 获取图片完整URL
  getImageUrl(url: string) {
    if (!url) {
return ''
}
    if (url.startsWith('http')) {
      return url
    }
    return `${config.baseURL}/${url}`
  }
}
</script>

<template>
  <div class="project-list-container">
    <!-- 页面标题 -->
    <div class="page-title">项目管理</div>

    <!-- 搜索栏 -->
    <div class="search-container">
      <el-form :inline="true" class="search-form">
        <el-form-item label="项目名称">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入项目名称"
            clearable
            @keyup.enter.native="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 工具栏 -->
    <div class="tool-container">
      <el-button type="primary" icon="el-icon-plus" @click="handleAddProject">新增项目</el-button>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="projectLists"
        v-loading="loading"
        border
        style="width: 100%"
        size="mini"
      >
        <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="title" label="项目名称" min-width="150"></el-table-column>
        <el-table-column prop="desc" label="项目描述" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="price" label="项目价格" width="120" align="center">
          <template slot-scope="scope">
            <span class="price-text">¥{{ scope.row.price || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vip_price" label="会员价格" width="120" align="center">
          <template slot-scope="scope">
            <span class="vip-price-text">¥{{ scope.row.vip_price || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="image" label="项目图片" width="100" align="center">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.image"
              :src="getImageUrl(scope.row.image)"
              style="width: 50px; height: 50px; border-radius: 4px;"
              fit="cover"
              :preview-src-list="[getImageUrl(scope.row.image)]"
            ></el-image>
            <span v-else class="no-image">暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEditProject(scope.row)">编辑</el-button>
            <el-button
              type="text"
              size="small"
              class="delete-btn"
              @click="handleDeleteProject(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <!-- <div class="pagination-container" v-if="projectLists.length > 0">
        <ls-pagination v-model="pager" @change="getProjectList()" />
      </div> -->

      <!-- 无数据提示 -->
      <div class="empty-project" v-if="projectLists.length === 0 && !loading">
        <el-empty description="暂无项目"></el-empty>
      </div>
    </div>

    <!-- 项目表单弹窗 -->
    <el-dialog
      :title="projectDialog.title"
      :visible.sync="projectDialog.visible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleCloseDialog"
    >
      <el-form
        ref="projectForm"
        :model="projectForm"
        :rules="projectRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="项目名称" prop="title">
          <el-input v-model="projectForm.title" placeholder="请输入项目名称"></el-input>
        </el-form-item>

        <el-form-item label="项目描述" prop="desc">
          <el-input
            v-model="projectForm.desc"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述"
          ></el-input>
        </el-form-item>

        <el-form-item label="项目价格" prop="price">
          <el-input v-model="projectForm.price" placeholder="请输入项目价格">
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="会员价格" prop="vip_price">
          <el-input v-model="projectForm.vip_price" placeholder="请输入会员价格">
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="项目图片" prop="image">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :on-error="handleImageError"
              accept="image/*"
              list-type="picture-card"
            >
              <img v-if="projectForm.image" :src="getImageUrl(projectForm.image)" class="uploaded-image">
              <i v-else class="el-icon-plus image-uploader-icon"></i>
            </el-upload>
            <div class="upload-tips">
              <p>点击上传项目图片</p>
              <p>支持 jpg、png 格式，大小不超过 2MB</p>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveProject" :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.project-list-container {
  padding: 20px;

  .page-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
  }

  .search-container {
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
  }

  .tool-container {
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
  }

  .table-container {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;

    .empty-project {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .delete-btn {
    color: #F56C6C;
  }

  .price-text {
    color: #E6A23C;
    font-weight: bold;
  }

  .vip-price-text {
    color: #F56C6C;
    font-weight: bold;
  }

  .no-image {
    color: #999;
    font-size: 12px;
  }

  .image-upload-container {
    display: flex;
    align-items: flex-start;
    gap: 15px;

    .image-uploader {
      .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: #409EFF;
        }
      }

      .uploaded-image {
        width: 120px;
        height: 120px;
        object-fit: cover;
        display: block;
      }

      .image-uploader-icon {
        font-size: 28px;
        color: #8c939d;
      }
    }

    .upload-tips {
      flex: 1;
      color: #999;
      font-size: 12px;
      line-height: 1.5;

      p {
        margin: 0 0 5px 0;
      }
    }
  }
}
</style>
