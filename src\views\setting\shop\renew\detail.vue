<template>
    <div>
        <div class="ls-card">
            <el-page-header @back="$router.go(-1)" content="订单详情" />
        </div>

        <!-- 头部 -->
        <div class="flex m-t-16">
            <!-- 订单信息 -->
            <div class="ls-card">
                <div style="width: 100%">
                    <div class="nr weight-500 m-b-20 title">订单信息</div>
                    <div class="flex col-top">
                        <el-form ref="form" :model="orderData" label-width="70px" size="small">
                            <el-form-item label="订单状态">
                                {{ orderData.order_status_desc }}
                            </el-form-item>
                            <el-form-item label="订单编号">
                                {{ orderData.sn }}
                            </el-form-item>
                            <el-form-item label="下单时间">
                                {{ orderData.create_time }}
                            </el-form-item>
                            <el-form-item label="完成时间">
                                {{ orderData.expires_time_desc }}
                            </el-form-item>
                        </el-form>
                        <el-form ref="form" style="margin-left: 20vw" :model="orderData" label-width="120px"
                            size="small">
                            <el-form-item label="支付状态">
                                <span :class="{ danger: orderData.pay_status == 0 }">
                                    {{ orderData.pay_status_desc }}
                                </span>
                            </el-form-item>
                            <el-form-item label="支付方式">
                                {{ orderData.pay_way_desc }}
                            </el-form-item>
                            <el-form-item label="付款时间">
                                {{ orderData.pay_time || '-' }}
                            </el-form-item>
                            <el-form-item label="后台备注">
                                {{ orderData.remark || '-' }}
                            </el-form-item>
                        </el-form>
                    </div>
                </div>

                <div class="m-t-16">
                    <el-button v-if="orderData.order_status == 0" size="small" type="primary"
                        @click="showPay = true">继续支付</el-button>
                    <ls-dialog v-if="orderData.order_status == 0" class="inline m-l-10" :content="`确定取消该订单?请谨慎操作`"
                        @confirm="orderCancel">
                        <el-button slot="trigger" size="small" style="primary">取消订单</el-button>
                    </ls-dialog>
                </div>
            </div>
        </div>
        <div class="ls-card m-t-16">
            <div class="nr weight-500 m-b-20 title">续费信息</div>
            <div>
                <el-table :data="[orderData]" ref="paneTable" style="width: 100%" size="mini">
                    <el-table-column label="商家名称">
                        <template slot-scope="scope">
                            <div class="flex">
                                <el-image class="flex-none" :src="scope.row.shop_logo"
                                    style="width: 40px; height: 40px">
                                </el-image>
                                <span class="m-l-10">{{ scope.row.shop_name }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="套餐名称" prop="set_meal_name"></el-table-column>
                    <el-table-column prop="time_desc" label="套餐时长"></el-table-column>
                    <el-table-column prop="expires_time_desc" label="到期时间"></el-table-column>
                    <el-table-column label="套餐价格">
                        <template slot-scope="scope">
                            <span>¥{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="实付金额">
                        <template slot-scope="scope">
                            <span>¥{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <pay-select v-model="showPay" :data="payWays" class="m-l-10" @confirm="pay($event)"> </pay-select>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import LsDialog from '@/components/ls-dialog.vue'
import { apiRenewCancel, apiRenewDetail, apiRenewPay, apiRenewPayStatus } from '@/api/setting/renew'
import PaySelect from '@/components/pay-select/dialog.vue'
import { createdPay } from '@/utils/pay'
import payMixin from '@/mixin/pay'
import { rotationCreate } from '@/utils/rotation'
@Component({
    components: {
        LsDialog,
        PaySelect
    },
    mixins: [payMixin]
})
export default class OrderDetail extends Vue {
    // S Data

    // 订单详情ID
    id: any = 0
    showPay = false
    // 订单数据
    orderData: any = {}

    // 商家备注
    remarks: String = ''

    // E Data

    // S Methods
    // 获取订单详情
    getOrderDetail() {
        apiRenewDetail({
            order_id: this.id
        }).then(res => {
            this.orderData = res
        })
    }
    async orderCancel() {
        await apiRenewCancel({
            order_id: this.id
        })
        this.getOrderDetail()
    }
    payStatus = rotationCreate(this.checkPay)
    async pay(payWay: number) {
        try {
            const res = await apiRenewPay({
                pay_way: payWay,
                order_id: this.id,
                redirect_url: '/admin/setting/shop/renew'
            })
            const pay = createdPay()
            const payModule = pay.getPayModules(payWay)
            this.payStatus.start()
            await payModule.pay(res, this.$createElement)
        } catch (error) {
            this.payStatus.stop()
            this.getOrderDetail()
        }
    }
    async checkPay(stop: any) {
        const res = await apiRenewPayStatus({ order_id: this.id })
        if (res.pay_status == 1) {
            stop()
            this.getOrderDetail()
            this.$msgbox.close()
        }
    }
    beforeDestroy() {
        this.payStatus.stop()
    }
    // E Methods

    created() {
        this.id = this.$route.query.id
        this.id && this.getOrderDetail()
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form .el-form-item {
    margin-bottom: 12px !important;
}

::v-deep .el-table__footer-wrapper tbody td {
    background: #fff !important;
}

::v-deep .el-table__footer-wrapper td {
    // border: 0;
}

::v-deep .el-table--border::after,
.el-table::before {
    position: static;
}

.username:hover {
    color: $--color-primary;
}

.title {
    width: 100%;
    padding-bottom: 20px;
    border-bottom: 1px solid #f2f2f2;
}
</style>
