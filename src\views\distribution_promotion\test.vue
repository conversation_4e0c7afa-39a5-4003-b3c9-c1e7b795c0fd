<template>
    <div class="api-test">
        <el-card>
            <div slot="header">
                <span>分销推广API测试</span>
            </div>
            
            <el-row :gutter="20">
                <el-col :span="12">
                    <h3>分销用户管理API</h3>
                    <el-button @click="testUserList" type="primary">测试用户列表</el-button>
                    <el-button @click="testUserDetail" type="primary">测试用户详情</el-button>
                    <el-button @click="testStatistics" type="primary">测试统计概览</el-button>
                    <el-button @click="testLevelStatistics" type="primary">测试等级统计</el-button>
                </el-col>
                
                <el-col :span="12">
                    <h3>提现管理API</h3>
                    <el-button @click="testWithdrawList" type="success">测试提现列表</el-button>
                    <el-button @click="testWithdrawStatistics" type="success">测试提现统计</el-button>
                </el-col>
            </el-row>
            
            <div class="result-area" v-if="testResult">
                <h3>测试结果：</h3>
                <pre>{{ testResult }}</pre>
            </div>
        </el-card>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { 
    apiDistributionPromotionUserList,
    apiDistributionPromotionUserDetail,
    apiDistributionPromotionStatistics,
    apiDistributionPromotionLevelStatistics,
    apiDistributionWithdrawList,
    apiDistributionWithdrawStatistics
} from '@/api/distribution_promotion/distribution_promotion'

@Component({
    name: 'DistributionPromotionTest'
})
export default class DistributionPromotionTest extends Vue {
    private testResult = ''

    // 测试用户列表API
    async testUserList() {
        try {
            this.testResult = '正在测试用户列表API...'
            const result = await apiDistributionPromotionUserList({
                page: 1,
                limit: 10
            })
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `用户列表API测试失败: ${error}`
        }
    }

    // 测试用户详情API
    async testUserDetail() {
        try {
            this.testResult = '正在测试用户详情API...'
            const result = await apiDistributionPromotionUserDetail({
                user_id: 1
            })
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `用户详情API测试失败: ${error}`
        }
    }

    // 测试统计概览API
    async testStatistics() {
        try {
            this.testResult = '正在测试统计概览API...'
            const result = await apiDistributionPromotionStatistics()
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `统计概览API测试失败: ${error}`
        }
    }

    // 测试等级统计API
    async testLevelStatistics() {
        try {
            this.testResult = '正在测试等级统计API...'
            const result = await apiDistributionPromotionLevelStatistics()
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `等级统计API测试失败: ${error}`
        }
    }

    // 测试提现列表API
    async testWithdrawList() {
        try {
            this.testResult = '正在测试提现列表API...'
            const result = await apiDistributionWithdrawList({
                page: 1,
                limit: 10
            })
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `提现列表API测试失败: ${error}`
        }
    }

    // 测试提现统计API
    async testWithdrawStatistics() {
        try {
            this.testResult = '正在测试提现统计API...'
            const result = await apiDistributionWithdrawStatistics()
            this.testResult = JSON.stringify(result, null, 2)
        } catch (error) {
            this.testResult = `提现统计API测试失败: ${error}`
        }
    }
}
</script>

<style lang="scss" scoped>
.api-test {
    padding: 20px;

    .el-button {
        margin: 5px;
    }

    .result-area {
        margin-top: 20px;
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 4px;

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
    }
}
</style>
