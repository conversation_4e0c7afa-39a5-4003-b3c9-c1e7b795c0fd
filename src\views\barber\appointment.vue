<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
  apiBarberList,
  apiBarberAppointmentList,
  apiAppointmentConfirm,
  apiAppointmentCancel,
  apiAppointmentComplete,
  apiAppointmentPayConfirm,
  apiAppointmentRefund
} from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'
import LsDialog from '@/components/ls-dialog.vue'

@Component({
  name: 'BarberAppointment',
  components: {
    LsPagination,
    LsDialog
  }
})
export default class BarberAppointment extends Vue {
  // 当前选中的理发师
  private currentBarber = {
    id: '',
    name: ''
  }

  // 理发师列表
  private barberList: any[] = []

  // 分页数据
  private pager = new RequestPaging()

  // 搜索理发师关键词
  private barberSearchKeyword = ''

  // 搜索预约关键词
  private appointmentSearchKeyword = ''

  // 日期范围
  private dateRange: any = []

  // 加载状态
  private loading = false

  // 对话框状态
  private dialogVisible = {
    confirm: false,
    cancel: false,
    complete: false,
    payConfirm: false,
    refund: false
  }

  // 当前操作的预约
  private currentAppointment: any = null

  // 表单数据
  private formData = {
    confirm: {
      remark: ''
    },
    cancel: {
      reason: '',
      refund_fee: true
    },
    complete: {
      service_note: ''
    },
    payConfirm: {
      pay_way: 1,
      pay_note: ''
    },
    refund: {
      refund_reason: '',
      refund_amount: 0
    }
  }

  // 支付方式选项
  private payWayOptions = [
    { label: '现金', value: 1 },
    { label: '微信', value: 2 },
    { label: '支付宝', value: 3 },
    { label: '银行卡', value: 4 }
  ]

  created() {
    this.getBarberList()
  }

  // 获取理发师列表
  async getBarberList() {
    try {
      this.loading = true
      const res = await apiBarberList({
        page_no: 1,
        page_size: 10000
      })
      this.barberList = res.lists || []
      if (this.barberList.length > 0) {
        // 默认选中第一个理发师
        this.handleSelectBarber(this.barberList[0])
      }
    } catch (error) {
      console.error('获取理发师列表失败', error)
    } finally {
      this.loading = false
    }
  }

  // 选择理发师
  handleSelectBarber(barber: any) {
    this.currentBarber = {
      id: barber.id,
      name: barber.name
    }
    this.getAppointmentList()
  }

  // 获取理发师预约列表
  async getAppointmentList() {
    if (!this.currentBarber.id) {
return
}

    try {
      this.loading = true
      this.pager.page = 1

      // 构建查询参数
      const params: any = {
        id: this.currentBarber.id
      }

      // 如果有关键词搜索
      if (this.appointmentSearchKeyword) {
        params.mobile = this.appointmentSearchKeyword
      }

      // 如果有日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        params.start_date = this.dateRange[0]
        params.end_date = this.dateRange[1]
      }

      await this.pager.request({
        callback: apiBarberAppointmentList,
        params
      })
    } catch (error) {
      console.error('获取理发师预约列表失败', error)
    } finally {
      this.loading = false
    }
  }

  // 处理理发师搜索
  handleBarberSearch() {
    if (!this.barberSearchKeyword) {
return
}

    const filtered = this.barberList.filter(item =>
      item.name.includes(this.barberSearchKeyword) ||
      (item.mobile && item.mobile.includes(this.barberSearchKeyword))
    )

    if (filtered.length > 0) {
      this.handleSelectBarber(filtered[0])
    }
  }

  // 处理预约搜索
  handleAppointmentSearch() {
    this.getAppointmentList()
  }

  // 重置搜索条件
  resetSearch() {
    this.appointmentSearchKeyword = ''
    this.dateRange = []
    this.getAppointmentList()
  }

  // 格式化时间
  formatDate(timestamp: number): string {
    if (!timestamp) {
return '-'
}
    const date = new Date(timestamp * 1000)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 格式化服务项目显示
  formatServices(services: string[]): string {
    if (!services || !services.length) {
      return '-'
    }
    return services.join(', ')
  }

  // 获取预约状态标签类型
  getStatusTagType(status: number): string {
    const statusMap: { [key: number]: string } = {
      0: 'info', // 待支付
      1: 'warning', // 待确认
      2: 'success', // 已确认
      3: 'danger', // 已取消
      4: 'primary' // 已完成
    }
    return statusMap[status] || 'info'
  }

  // 确认预约
  async handleConfirmAppointment(appointment: any) {
    this.currentAppointment = appointment
    this.formData.confirm.remark = ''
    this.dialogVisible.confirm = true
  }

  // 提交确认预约
  async submitConfirmAppointment() {
    if (!this.currentAppointment) {
return
}

    try {
      this.loading = true
      await apiAppointmentConfirm({
        id: this.currentAppointment.id
      }, {
        remark: this.formData.confirm.remark
      })
      this.$message.success('预约确认成功')
      this.dialogVisible.confirm = false
      this.getAppointmentList()
    } catch (error) {
      console.error('确认预约失败', error)
      this.$message.error('确认预约失败')
    } finally {
      this.loading = false
    }
  }

  // 取消预约
  async handleCancelAppointment(appointment: any) {
    this.currentAppointment = appointment
    this.formData.cancel.reason = ''
    this.formData.cancel.refund_fee = true
    this.dialogVisible.cancel = true
  }

  // 提交取消预约
  async submitCancelAppointment() {
    if (!this.currentAppointment) {
return
}

    if (!this.formData.cancel.reason.trim()) {
      this.$message.error('请输入取消原因')
      return
    }

    try {
      this.loading = true
      await apiAppointmentCancel({
        id: this.currentAppointment.id
      }, {
        reason: this.formData.cancel.reason,
        refund_fee: this.formData.cancel.refund_fee
      })
      this.$message.success('预约取消成功')
      this.dialogVisible.cancel = false
      this.getAppointmentList()
    } catch (error) {
      console.error('取消预约失败', error)
      this.$message.error('取消预约失败')
    } finally {
      this.loading = false
    }
  }

  // 完成预约
  async handleCompleteAppointment(appointment: any) {
    this.currentAppointment = appointment
    this.formData.complete.service_note = ''
    this.dialogVisible.complete = true
  }

  // 提交完成预约
  async submitCompleteAppointment() {
    if (!this.currentAppointment) {
return
}

    try {
      this.loading = true
      await apiAppointmentComplete({
        id: this.currentAppointment.id
      }, {
        service_note: this.formData.complete.service_note
      })
      this.$message.success('预约已标记为完成')
      this.dialogVisible.complete = false
      this.getAppointmentList()
    } catch (error) {
      console.error('完成预约失败', error)
      this.$message.error('完成预约失败')
    } finally {
      this.loading = false
    }
  }

  // 确认支付
  async handlePayConfirm(appointment: any) {
    this.currentAppointment = appointment
    this.formData.payConfirm.pay_way = 1
    this.formData.payConfirm.pay_note = ''
    this.dialogVisible.payConfirm = true
  }

  // 提交确认支付
  async submitPayConfirm() {
    if (!this.currentAppointment) {
return
}

    if (!this.formData.payConfirm.pay_way) {
      this.$message.error('请选择支付方式')
      return
    }

    try {
      this.loading = true
      await apiAppointmentPayConfirm({
        id: this.currentAppointment.id
      }, {
        pay_way: this.formData.payConfirm.pay_way,
        pay_note: this.formData.payConfirm.pay_note
      })
      this.$message.success('支付确认成功')
      this.dialogVisible.payConfirm = false
      this.getAppointmentList()
    } catch (error) {
      console.error('确认支付失败', error)
      this.$message.error('确认支付失败')
    } finally {
      this.loading = false
    }
  }

  // 退还预约费
  async handleRefund(appointment: any) {
    this.currentAppointment = appointment
    this.formData.refund.refund_reason = ''
    this.formData.refund.refund_amount = appointment.reservation_fee || 0
    this.dialogVisible.refund = true
  }

  // 提交退还预约费
  async submitRefund() {
    if (!this.currentAppointment) {
return
}

    if (!this.formData.refund.refund_reason.trim()) {
      this.$message.error('请输入退费原因')
      return
    }

    if (this.formData.refund.refund_amount <= 0) {
      this.$message.error('退费金额必须大于0')
      return
    }

    try {
      this.loading = true
      await apiAppointmentRefund({
        id: this.currentAppointment.id
      }, {
        refund_reason: this.formData.refund.refund_reason,
        refund_amount: this.formData.refund.refund_amount
      })
      this.$message.success('退费成功')
      this.dialogVisible.refund = false
      this.getAppointmentList()
    } catch (error) {
      console.error('退费失败', error)
      this.$message.error('退费失败')
    } finally {
      this.loading = false
    }
  }
}
</script>

<template>
  <div class="barber-appointment-wrapper">
    <div class="barber-appointment-container">
      <!-- 页面标题 -->
      <div class="page-title">理发师预约管理</div>

      <div class="barber-appointment-content">
        <!-- 左侧理发师列表 -->
        <div class="barber-list-panel">
          <div class="panel-title">理发师列表</div>
          <el-input
            v-model="barberSearchKeyword"
            placeholder="搜索理发师"
            prefix-icon="el-icon-search"
            class="barber-search"
            clearable
            @keyup.enter.native="handleBarberSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleBarberSearch"></el-button>
          </el-input>
          <div class="barber-items">
            <div
              v-for="item in barberList"
              :key="item.id"
              class="barber-item"
              :class="{ 'active': currentBarber.id === item.id }"
              @click="handleSelectBarber(item)"
            >
              <el-avatar :src="item.image" :size="36"></el-avatar>
              <div class="barber-name">{{ item.name }}</div>
            </div>
            <div v-if="barberList.length === 0" class="empty-tip">
              暂无理发师
            </div>
          </div>
        </div>

        <!-- 右侧预约列表 -->
        <div class="appointment-panel">
          <div class="panel-header">
            <div class="panel-title">
              {{ currentBarber.name ? `${currentBarber.name}的预约` : '请选择理发师' }}
            </div>
            <div class="panel-actions">
              <!-- <el-date-picker
                v-model="dateRange"
                type="daterange"
                size="small"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 300px; margin-right: 10px;"
              ></el-date-picker> -->
              <el-input
                v-model="appointmentSearchKeyword"
                placeholder="搜索联系人/手机号"
                clearable
                size="small"
                style="width: 200px; margin-right: 10px;"
                @keyup.enter.native="handleAppointmentSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleAppointmentSearch"></el-button>
              </el-input>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-search"
                @click="handleAppointmentSearch"
              >
                搜索
              </el-button>
              <el-button
                size="small"
                @click="resetSearch"
              >
                重置
              </el-button>
            </div>
          </div>

          <!-- 预约表格 -->
          <div class="appointment-table" v-loading="loading">
            <el-table
              :data="pager.lists"
              border
              style="width: 100%"
              size="small"
              :max-height="600"
            >
              <el-table-column prop="id" label="ID" width="60"></el-table-column>
              <el-table-column label="预约信息" width="160">
                <template slot-scope="scope">
                  <div class="appointment-info">
                    <div class="appointment-item">
                      <span class="label">联系人：</span>
                      <span class="value">{{ scope.row.contact }}</span>
                    </div>
                    <div class="appointment-item">
                      <span class="label">手机号：</span>
                      <span class="value">{{ scope.row.mobile }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="预约时间" width="160">
                <template slot-scope="scope">
                  <div class="appointment-date">
                    <el-tag size="small">{{ scope.row.data }}</el-tag>
                    <el-tag size="small" type="success" style="margin-left: 5px;">{{ scope.row.time }}</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="预约项目" width="150">
                <template slot-scope="scope">
                  <div class="appointment-services">
                    <el-tag
                      v-for="(service, index) in scope.row.services_title"
                      :key="index"
                      size="small"
                      type="info"
                      style="margin-right: 5px; margin-bottom: 5px;"
                    >
                      {{ service }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="价格" width="80">
                <template slot-scope="scope">
                  ¥{{ scope.row.price }}
                </template>
              </el-table-column>
              <el-table-column label="预约费" width="100">
                <template slot-scope="scope">
                  <div v-if="scope.row.reservation_fee">
                    <div>¥{{ scope.row.reservation_fee }}</div>
                    <el-tag
                      size="mini"
                      :type="scope.row.fee_paid ? 'success' : 'danger'"
                    >
                      {{ scope.row.fee_paid ? '已支付' : '未支付' }}
                    </el-tag>
                  </div>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag
                    size="small"
                    :type="getStatusTagType(scope.row.status)"
                  >
                    {{ scope.row.status_desc }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column label="创建时间" width="150">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.create_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="160" fixed="right">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <!-- 确认预约 -->
                    <el-button
                      v-if="scope.row.status === 1 && scope.row.fee_paid"
                      type="text"
                      size="small"
                      @click="handleConfirmAppointment(scope.row)"
                    >
                      确认预约
                    </el-button>

                    <!-- 确认支付 -->
                    <el-button
                      v-if="scope.row.status === 0 && !scope.row.fee_paid"
                      type="text"
                      size="small"
                      @click="handlePayConfirm(scope.row)"
                    >
                      确认支付
                    </el-button>

                    <!-- 完成预约 -->
                    <el-button
                      v-if="scope.row.status === 2"
                      type="text"
                      size="small"
                      @click="handleCompleteAppointment(scope.row)"
                    >
                      完成预约
                    </el-button>

                    <!-- 取消预约 -->
                    <el-button
                      v-if="scope.row.status < 3"
                      type="text"
                      size="small"
                      @click="handleCancelAppointment(scope.row)"
                    >
                      取消预约
                    </el-button>

                    <!-- 退还预约费 -->
                    <el-button
                      v-if="scope.row.status === 3 && scope.row.fee_paid && !scope.row.fee_refunded"
                      type="text"
                      size="small"
                      @click="handleRefund(scope.row)"
                    >
                      退还预约费
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" v-if="currentBarber.id && pager.lists.length > 0">
              <ls-pagination v-model="pager" @change="getAppointmentList()" />
            </div>

            <!-- 未选择理发师或无预约时的提示 -->
            <div class="empty-appointment" v-if="currentBarber.id && pager.lists.length === 0">
              <el-empty description="暂无预约"></el-empty>
            </div>
            <div class="empty-appointment" v-if="!currentBarber.id">
              <el-empty description="请先选择理发师"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认预约对话框 -->
    <el-dialog
      title="确认预约"
      :visible.sync="dialogVisible.confirm"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="备注">
          <el-input
            v-model="formData.confirm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入确认备注（可选）"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible.confirm = false">取 消</el-button>
        <el-button type="primary" @click="submitConfirmAppointment" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog
      title="取消预约"
      :visible.sync="dialogVisible.cancel"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="取消原因" required>
          <el-input
            v-model="formData.cancel.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入取消原因"
          ></el-input>
        </el-form-item>
        <el-form-item label="退还预约费">
          <el-switch v-model="formData.cancel.refund_fee"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible.cancel = false">取 消</el-button>
        <el-button type="primary" @click="submitCancelAppointment" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 完成预约对话框 -->
    <el-dialog
      title="完成预约"
      :visible.sync="dialogVisible.complete"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="服务备注">
          <el-input
            v-model="formData.complete.service_note"
            type="textarea"
            :rows="3"
            placeholder="请输入服务备注（可选）"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible.complete = false">取 消</el-button>
        <el-button type="primary" @click="submitCompleteAppointment" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 确认支付对话框 -->
    <el-dialog
      title="确认预约费支付"
      :visible.sync="dialogVisible.payConfirm"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="支付方式" required>
          <el-select v-model="formData.payConfirm.pay_way" placeholder="请选择支付方式">
            <el-option
              v-for="item in payWayOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付备注">
          <el-input
            v-model="formData.payConfirm.pay_note"
            type="textarea"
            :rows="2"
            placeholder="请输入支付备注（可选）"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible.payConfirm = false">取 消</el-button>
        <el-button type="primary" @click="submitPayConfirm" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 退还预约费对话框 -->
    <el-dialog
      title="退还预约费"
      :visible.sync="dialogVisible.refund"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="退费原因" required>
          <el-input
            v-model="formData.refund.refund_reason"
            type="textarea"
            :rows="3"
            placeholder="请输入退费原因"
          ></el-input>
        </el-form-item>
        <el-form-item label="退费金额" required>
          <el-input-number
            v-model="formData.refund.refund_amount"
            :precision="2"
            :step="0.01"
            :min="0"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible.refund = false">取 消</el-button>
        <el-button type="primary" @click="submitRefund" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.barber-appointment-wrapper {
  .barber-appointment-container {
    padding: 20px;

  .page-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
  }

  .barber-appointment-content {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    min-height: 600px;
    overflow: hidden;

    .barber-list-panel {
      width: 320px;
      min-width: 320px;
      max-width: 320px;
      border-right: 1px solid #ebeef5;
      padding: 15px;
      box-sizing: border-box;

      .panel-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
      }

      .barber-search {
        margin-bottom: 15px;
      }

      .barber-items {
        max-height: 600px;
        overflow-y: auto;

        .barber-item {
          display: flex;
          align-items: center;
          padding: 12px;
          cursor: pointer;
          border-radius: 4px;
          margin-bottom: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #ecf5ff;
          }

          .barber-name {
            margin-left: 10px;
            font-size: 15px;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 220px;
          }
        }

        .empty-tip {
          text-align: center;
          color: #909399;
          padding: 20px 0;
        }
      }
    }

    .appointment-panel {
      flex: 1;
      padding: 15px;
      overflow: hidden;
      min-width: 0;

      .panel-header {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;

        .panel-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .panel-actions {
          display: flex;
          align-items: center;
        }
      }

      .appointment-table {
        overflow-x: auto;

        .appointment-info {
          .appointment-item {
            margin-bottom: 5px;

            .label {
              color: #909399;
            }

            .value {
              font-weight: 600;
            }
          }
        }

        .appointment-date {
          display: flex;
          align-items: center;
        }

        .appointment-services {
          display: flex;
          flex-wrap: wrap;
        }

        .action-buttons {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .el-button {
            margin: 0;
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }

      .empty-appointment {
        margin-top: 50px;
      }
    }
  }
  }
}
</style>
