import * as Common from '../common'

/** 分销用户管理 **/

// 分销用户列表请求参数
export interface DistributionUserListReq extends Common.Indexes {
    level?: number // 等级筛选
    status?: number // 状态筛选
    nickname?: string // 昵称搜索
    mobile?: string // 手机号搜索
    page?: number // 页码
    limit?: number // 每页数量
}

// 分销用户信息
export interface DistributionUser {
    user_id: number
    nickname: string
    mobile: string
    level: number
    level_name: string
    invite_count: number
    total_commission: number
    available_amount: number
    status_text: string
    parent_nickname: string
    create_time: string
}

// 分销用户列表响应
export interface DistributionUserListRes extends Common.Paging_Res {
    list: DistributionUser[]
    total: number
}

// 分销用户详情请求参数
export interface DistributionUserDetailReq {
    user_id: number
}

// 佣金统计
export interface CommissionStats {
    total_commission: number
    settled_commission: number
    pending_commission: number
}

// 提现统计
export interface WithdrawStats {
    total_withdraw: number
    success_withdraw: number
    pending_withdraw: number
}

// 分销用户详情响应
export interface DistributionUserDetailRes {
    user_id: number
    nickname: string
    mobile: string
    level: number
    invite_count: number
    total_commission: number
    available_amount: number
    first_children: DistributionUser[]
    second_children: DistributionUser[]
    commission_stats: CommissionStats
    withdraw_stats: WithdrawStats
}

// 分销统计概览
export interface DistributionStatistics {
    total: {
        users: number
        active_users: number
        invites: number
        commission: number
        withdraw: number
    }
    today: {
        users: number
        invites: number
        commission: number
    }
    month: {
        users: number
        invites: number
        commission: number
    }
    withdraw: {
        total: number
        pending: number
        success: number
    }
}

// 等级分布统计
export interface LevelStatistics {
    level: number
    level_name: string
    user_count: number
    min_invite: number
    max_invite: number
    first_rate: number
    second_rate: number
    invite_reward: number
}

// 状态变更请求参数
export interface ChangeStatusReq {
    user_id: number
    status: number // 0=禁用，1=启用
}

/** 提现管理 **/

// 提现申请列表请求参数
export interface WithdrawListReq extends Common.Indexes {
    status?: number // 状态筛选
    nickname?: string // 用户昵称搜索
    mobile?: string // 手机号搜索
    page?: number // 页码
    limit?: number // 每页数量
}

// 提现申请信息
export interface WithdrawApplication {
    id: number
    nickname: string
    mobile: string
    amount: number
    account_type_name: string
    status_name: string
    create_time: string
    audit_time: string
    audit_admin_name: string
}

// 提现申请列表响应
export interface WithdrawListRes extends Common.Paging_Res {
    list: WithdrawApplication[]
    total: number
}

// 审核提现申请请求参数
export interface WithdrawAuditReq {
    id: number
    status: number // 1=通过，2=拒绝
    remark: string // 审核备注
}

// 提现统计
export interface WithdrawStatistics {
    total: { amount: number; count: number }
    pending: { amount: number; count: number }
    success: { amount: number; count: number }
    reject: { amount: number; count: number }
    today: { amount: number; count: number }
    month: { amount: number; count: number }
    account_type_stats: Array<{
        type: number
        type_name: string
        amount: number
        count: number
    }>
}
