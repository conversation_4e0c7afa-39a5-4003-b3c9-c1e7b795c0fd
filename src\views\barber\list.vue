<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiBarberList, apiBarberDel, apiSubShopList } from '@/api/barber/barber'
import { RequestPaging } from '@/utils/util'
import LsPagination from '@/components/ls-pagination.vue'

@Component({
    name: 'barber_list',
    components: {
        LsPagination
    }
})
export default class BarberList extends Vue {
    // 搜索表单
    private searchForm = {
        name: '',
        mobile: '',
        shop_id: ''
    }

    // 分页数据
    private pager = new RequestPaging()

    // 店铺列表
    private shopList: any[] = []

    created() {
        this.getList()
        this.getShopList()
    }

    // 页面被激活时触发（从缓存中恢复）
    activated() {
        this.getList()
    }

    // 获取理发师列表
    async getList() {
        try {
            await this.pager.request({
                callback: apiBarberList,
                params: this.searchForm
            })
        } catch (error) {
            console.error('获取理发师列表失败', error)
        }
    }

    // 获取店铺列表
    async getShopList() {
        try {
            const res = await apiSubShopList({})
            if (res && res.lists) {
                this.shopList = res.lists
            }
        } catch (error) {
            console.error('获取店铺列表失败', error)
        }
    }

    // 搜索
    handleSearch() {
        this.pager.page = 1
        this.getList()
    }

    // 重置搜索
    resetSearch() {
        this.searchForm.name = ''
        this.searchForm.mobile = ''
        this.searchForm.shop_id = ''
        this.handleSearch()
    }

    // 新增理发师
    handleAdd() {
        this.$router.push('/barber/list_detail')
    }

    // 编辑理发师
    handleEdit(row: any) {
        this.$router.push(`/barber/list_detail?id=${row.id}`)
    }

    // 查看理发师预约
    handleViewAppointments(row: any) {
        this.$router.push('/barber/appointment')
    }

    // 删除理发师
    async handleDelete(row: any) {
        try {
            await this.$confirm('确认删除该理发师?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            console.log('row', row)

            try {
                const res = await apiBarberDel({ id: row.id })
                console.log('删除响应:', res)

                // 删除成功
                this.$message.success('删除成功')
                this.getList()
            } catch (error) {
                console.error('删除失败:', error)
                this.$message.error('删除失败')
            }
        } catch (error) {
            console.log('取消删除')
        }
    }
}
</script>

<template>
    <div class="barber-list-container">
        <!-- 页面标题 -->
        <div class="page-title">理发师列表</div>

        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="理发师名称">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入理发师名称"
                        clearable
                        @keyup.enter.native="handleSearch"
                    ></el-input>
                </el-form-item>
                <el-form-item label="手机号">
                    <el-input
                        v-model="searchForm.mobile"
                        placeholder="请输入手机号"
                        clearable
                        @keyup.enter.native="handleSearch"
                    ></el-input>
                </el-form-item>
                <el-form-item label="所属店铺">
                    <el-select v-model="searchForm.shop_id" placeholder="请选择店铺" clearable>
                        <el-option
                            v-for="item in shopList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <div class="tool-container">
            <el-button type="primary" @click="handleAdd">新增理发师</el-button>
        </div>

        <!-- 表格 -->
        <div class="table-container">
            <el-table
                :data="pager.lists"
                v-loading="pager.loading"
                border
                style="width: 100%"
                size="mini"
            >
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column label="理发师信息" min-width="200">
                    <template slot-scope="scope">
                        <div class="barber-info">
                            <el-image
                                class="barber-avatar"
                                :src="scope.row.image"
                                fit="cover"
                                style="width: 60px; height: 60px; border-radius: 50%"
                            ></el-image>
                            <div class="barber-detail">
                                <div class="barber-name">{{ scope.row.name }}</div>
                                <div class="barber-mobile">{{ scope.row.mobile }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="job_name" label="职位"></el-table-column>
                <el-table-column prop="account" label="登录账号"></el-table-column>
                <el-table-column prop="shop_name" label="所属店铺"></el-table-column>
                <el-table-column label="操作" width="220">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="small"
                            @click="handleEdit(scope.row)"
                        >编辑</el-button>
<!--                        <el-button-->
<!--                            type="text"-->
<!--                            size="small"-->
<!--                            @click="handleViewAppointments(scope.row)"-->
<!--                        >预约</el-button>-->
                        <el-button
                            type="text"
                            size="small"
                            @click="handleDelete(scope.row)"
                            class="delete-btn"
                        >删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <ls-pagination v-model="pager" @change="getList()" />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.barber-list-container {
    padding: 20px;

    .page-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .search-container {
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .tool-container {
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .table-container {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .barber-info {
        display: flex;
        align-items: center;

        .barber-detail {
            margin-left: 10px;

            .barber-name {
                font-weight: bold;
                margin-bottom: 5px;
            }

            .barber-mobile {
                color: #999;
                font-size: 12px;
            }
        }
    }

    .delete-btn {
        color: #F56C6C;
    }
}
</style>

