import request from '@/plugins/axios'


// 会员列表
// 示例
/*

{
    "code": 1,
    "show": 1,
    "msg": "成功",
    "data": {
        "count": 1,
        "page_no": "1",
        "page_size": "10",
        "lists": [
            {
                "id": 2,
                "mobile": "13800138000",  // 手机号
                "state": 0,
                "points": 0,    // 积分
                "nickname": "未来可期",
                "avatar": "uploads/2yjp63v5/user/images/20250415/20250415130950160ff6750.jpeg",
                "sn": "96407955"        // 会员编码
            }
        ]
    }
}
*/
export const apiMemberList = (params: any) => request.get('/vip_user/lists', { params })


// 用户会员卡列表
/*

id；会员id

{
    "code": 1,
    "show": 1,
    "msg": "成功",
    "data": {
        "count": 3,
        "page_no": 1,
        "page_size": 10,
        "lists": [
            {
                "id": 1,
                "vuid": 2,
                "price": "100",             "金额"
                "used_price": "0",          "已用金额"
                "remain_price": "100",      "剩余金额"
                "title": "",
                "num": "",
                "used_num": "",
                "remain_num": "",
                "create_time": 1747036171,
                "type": 1                   "类型 1会员卡  2次卡"
            },
            {
                "id": 2,
                "vuid": 2,
                "price": "",
                "used_price": "",
                "remain_price": "",
                "title": "剪发卡",          "会员卡名称"
                "num": "10",                "总次数"
                "used_num": "0",            "已用次数"
                "remain_num": "10",         "剩余次数"
                "create_time": 1747036231,
                "type": 2
            },
            {
                "id": 3,
                "vuid": 2,
                "price": "",
                "used_price": "",
                "remain_price": "",
                "title": "染发卡",
                "num": "10",
                "used_num": "0",
                "remain_num": "10",
                "create_time": 1747036269,
                "type": 2
            }
        ]
    }
}
*/
export const apiUserCardList = (params: any) => request.get('/vip_user/card_list', { params })

// 修改会员手机号积分

/*

id：会员（人）id
mobile：手机号
type：1增加积分，2减少积分
points：积分

*/
export const apiUpdateMemberPhone = (params: any) => request.post('/vip_user/edit', params)

// 会员开卡

/*
id：会员（人）id
type：卡类型：1=会员卡，2=次卡
salesperson_id：销售人员ID（理发师ID）- 会员卡和次卡都必填
price：金额（type=1时必填）
title：次卡名称（type=2时必填）
num：次数（type=2时必填）
totl_price：总金额（type=2时必填）
single_price：单次金额（type=2时必填）
mobile：手机号（开卡时必须填写手机号，如果开过卡，那就已经绑定过手机号直接用绑定的手机号）
*/
export const apiMemberOpenCard = (params: any) => request.post('/vip_user/open_card', params)


// 使用记录
/*
service_title：服务名称
price：服务价格/金额
create_time：服务时间
card_id：卡id
type：记录类型 1=充值/开卡，2=消费/使用
remark：使用描述


*/
export const apiMemberUsageRecord = (params: any) => request.get('/vip_user/card_used', { params })
