import { ActionTree, Module, MutationTree } from 'vuex'
import { AppState, RootState } from '../type'
import { GetterTree } from 'vuex'
import { apiAuth, apiconfig } from '@/api/app'
import { apiAppModule, apiMarketingModule } from '@/api/marketing'

const state: AppState = {
    permission: {},
    config: {},
    comboAuth: []
}

const getters: GetterTree<AppState, RootState> = {
    permission: state => state.permission,
    config: state => state.config,
    comboAuth: state => state.comboAuth
}

const mutations: MutationTree<AppState> = {
    /**
     * @description 设置权限
     * @param { Object } state
     * @param { Array } data
     */
    setPermission(state, data) {
        state.permission = data
    },
    /**
     * @description 设置配置
     * @param { Object } state
     * @param { Array } data
     */
    setCoonfig(state, data) {
        state.config = data
    },
    setComboAuth(state, data) {
        state.comboAuth.push(data)
    }
}

const actions: ActionTree<AppState, RootState> = {
    /**
     * @description 获取权限
     * @param { Object } state
     * @param { Function } commit
     */
    getPermission({ state, commit }) {
        return apiAuth().then(res => {
            commit('setPermission', res)
            return Promise.resolve(res)
        })
    },
    getConfig({ state, commit }) {
        return apiconfig().then(res => {
            commit('setCoonfig', res)
            return Promise.resolve(res)
        })
    },
    getComboAuth({ state, commit }) {
        apiAppModule().then(res => {
            res.map((item: any) => {
                item.list.map((i: any) => {
                    commit('setComboAuth', i.name)
                })
            })
            apiMarketingModule().then(res => {
                res.map((item: any) => {
                    item.list.map((i: any) => {
                        commit('setComboAuth', i.name)
                    })
                })
            })
        })
    }
}

const app: Module<AppState, RootState> = {
    state,
    mutations,
    actions,
    getters
}

export default app
