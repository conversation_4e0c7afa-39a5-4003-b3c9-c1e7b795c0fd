<template>
    <div class="distribution-user-list">
        <!-- 搜索筛选 -->
        <div class="search-container">
            <el-card>
                <el-form :model="searchForm" inline>
                    <el-form-item label="昵称">
                        <el-input
                            v-model="searchForm.nickname"
                            placeholder="请输入用户昵称"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="手机号">
                        <el-input
                            v-model="searchForm.mobile"
                            placeholder="请输入手机号"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="等级">
                        <el-select
                            v-model="searchForm.level"
                            placeholder="请选择等级"
                            clearable
                            style="width: 150px"
                        >
                            <el-option
                                v-for="level in levelOptions"
                                :key="level.value"
                                :label="level.label"
                                :value="level.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="请选择状态"
                            clearable
                            style="width: 150px"
                        >
                            <el-option label="启用" :value="1" />
                            <el-option label="禁用" :value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <el-card>
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column prop="user_id" label="用户ID" width="80" />
                    <el-table-column prop="nickname" label="昵称" min-width="120" />
                    <el-table-column prop="mobile" label="手机号" width="130" />
                    <el-table-column prop="level_name" label="等级" width="120" />
                    <el-table-column prop="invite_count" label="邀请人数" width="100" />
                    <el-table-column prop="total_commission" label="总佣金" width="120">
                        <template slot-scope="scope">
                            ¥{{ scope.row.total_commission }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="available_amount" label="可用余额" width="120">
                        <template slot-scope="scope">
                            ¥{{ scope.row.available_amount }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="parent_nickname" label="上级" width="120" />
                    <el-table-column prop="status_text" label="状态" width="80">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status_text === '启用' ? 'success' : 'danger'">
                                {{ scope.row.status_text }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="create_time" label="注册时间" width="160" />
                    <el-table-column label="操作" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                size="small"
                                @click="handleViewDetail(scope.row)"
                            >
                                查看详情
                            </el-button>
                            <el-button
                                type="text"
                                size="small"
                                :class="scope.row.status_text === '启用' ? 'danger' : 'success'"
                                @click="handleChangeStatus(scope.row)"
                            >
                                {{ scope.row.status_text === '启用' ? '禁用' : '启用' }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="pagination.page"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pagination.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total"
                    />
                </div>
            </el-card>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { 
    apiDistributionPromotionUserList, 
    apiDistributionPromotionChangeStatus 
} from '@/api/distribution_promotion/distribution_promotion'
import * as Interface from '@/api/distribution_promotion/distribution_promotion.d'

@Component({
    name: 'DistributionUserList'
})
export default class DistributionUserList extends Vue {
    // 搜索表单
    private searchForm: Interface.DistributionUserListReq = {
        nickname: '',
        mobile: '',
        level: undefined,
        status: undefined,
        page: 1,
        limit: 20
    }

    // 表格数据
    private tableData: Interface.DistributionUser[] = []

    // 分页信息
    private pagination = {
        page: 1,
        limit: 20,
        total: 0
    }

    // 加载状态
    private loading = false

    // 等级选项
    private levelOptions = [
        { label: '青铜分销商', value: 1 },
        { label: '白银分销商', value: 2 },
        { label: '黄金分销商', value: 3 },
        { label: '钻石分销商', value: 4 }
    ]

    created() {
        this.getUserList()
    }

    // 获取用户列表
    async getUserList() {
        try {
            this.loading = true
            const params = {
                ...this.searchForm,
                page: this.pagination.page,
                limit: this.pagination.limit
            }
            const res = await apiDistributionPromotionUserList(params)
            if (res && res.list) {
                this.tableData = res.list
                this.pagination.total = res.total || 0
            }
        } catch (error) {
            console.error('获取分销用户列表失败', error)
            this.$message.error('获取分销用户列表失败')
        } finally {
            this.loading = false
        }
    }

    // 搜索
    handleSearch() {
        this.pagination.page = 1
        this.getUserList()
    }

    // 重置
    handleReset() {
        this.searchForm = {
            nickname: '',
            mobile: '',
            level: undefined,
            status: undefined,
            page: 1,
            limit: 20
        }
        this.pagination.page = 1
        this.getUserList()
    }

    // 查看详情
    handleViewDetail(row: Interface.DistributionUser) {
        this.$router.push({
            path: '/distribution_promotion/user_detail',
            query: { user_id: row.user_id.toString() }
        })
    }

    // 改变状态
    async handleChangeStatus(row: Interface.DistributionUser) {
        const action = row.status_text === '启用' ? '禁用' : '启用'
        try {
            await this.$confirm(`确定要${action}该用户吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })

            const status = row.status_text === '启用' ? 0 : 1
            await apiDistributionPromotionChangeStatus({
                user_id: row.user_id,
                status
            })

            this.$message.success(`${action}成功`)
            this.getUserList()
        } catch (error) {
            if (error !== 'cancel') {
                console.error('状态变更失败', error)
                this.$message.error('状态变更失败')
            }
        }
    }

    // 分页大小改变
    handleSizeChange(val: number) {
        this.pagination.limit = val
        this.pagination.page = 1
        this.getUserList()
    }

    // 当前页改变
    handleCurrentChange(val: number) {
        this.pagination.page = val
        this.getUserList()
    }
}
</script>

<style lang="scss" scoped>
.distribution-user-list {
    padding: 20px;

    .search-container {
        margin-bottom: 20px;
    }

    .table-container {
        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    }

    .el-button.danger {
        color: #f56c6c;
    }

    .el-button.success {
        color: #67c23a;
    }
}
</style>
