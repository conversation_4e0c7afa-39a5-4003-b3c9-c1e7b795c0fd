<template>
    <div class="business-statistics">
        <!-- 时间筛选器 -->
        <div class="filter-section">
            <div class="filter-title">营业统计</div>
            <div class="filter-controls">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    @change="onDateRangeChange"
                    size="small"
                >
                </el-date-picker>
                <el-button 
                    type="primary" 
                    size="small" 
                    @click="refreshData"
                    :loading="loading"
                    class="refresh-btn"
                >
                    刷新
                </el-button>
            </div>
        </div>

        <!-- 营业汇总 -->
        <div class="summary-section" v-loading="loading">
            <div class="section-title">营业汇总</div>
            <el-row :gutter="20" v-if="summaryData">
                <el-col :span="6">
                    <div class="stat-card">
                        <div class="stat-label">总营业额</div>
                        <div class="stat-value">¥{{ summaryData.total_amount || 0 }}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-card">
                        <div class="stat-label">订单数量</div>
                        <div class="stat-value">{{ summaryData.order_count || 0 }}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-card">
                        <div class="stat-label">服务收入</div>
                        <div class="stat-value">¥{{ summaryData.service_amount || 0 }}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="stat-card">
                        <div class="stat-label">商品收入</div>
                        <div class="stat-value">¥{{ summaryData.goods_amount || 0 }}</div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <!-- 业绩排名 -->
        <div class="rank-section" v-loading="loading">
            <div class="section-title">业绩排名</div>
            <el-table 
                :data="rankData" 
                size="small"
                v-if="rankData && rankData.length > 0"
            >
                <el-table-column label="排名" width="80" align="center">
                    <template slot-scope="scope">
                        <div class="rank-badge" :class="getRankClass(scope.$index)">
                            {{ scope.$index + 1 }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="barber_name" label="理发师" min-width="120">
                </el-table-column>
                <el-table-column prop="order_count" label="订单数" width="100" align="center">
                </el-table-column>
                <el-table-column prop="total_amount" label="业绩金额" width="120" align="right">
                    <template slot-scope="scope">
                        ¥{{ scope.row.total_amount || 0 }}
                    </template>
                </el-table-column>
                <el-table-column prop="commission_amount" label="提成金额" width="120" align="right">
                    <template slot-scope="scope">
                        ¥{{ scope.row.commission_amount || 0 }}
                    </template>
                </el-table-column>
            </el-table>
            <div v-else class="no-data">
                暂无业绩数据
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { apiWorkbenchSummary, apiWorkbenchRank } from '@/api/home'

@Component({
    name: 'BusinessStatistics'
})
export default class BusinessStatistics extends Vue {
    @Prop({ default: null }) adminInfo!: any

    // 数据属性
    private dateRange: string[] = []
    private loading = false
    private summaryData: any = null
    private rankData: any[] = []

    // 生命周期
    mounted() {
        this.initDateRange()
        this.loadData()
    }

    // 初始化日期范围（默认最近7天）
    initDateRange() {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 6) // 最近7天
        
        this.dateRange = [
            this.formatDate(start),
            this.formatDate(end)
        ]
    }

    // 格式化日期
    formatDate(date: Date): string {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
    }

    // 日期范围改变
    onDateRangeChange(value: string[]) {
        if (value && value.length === 2) {
            this.loadData()
        }
    }

    // 刷新数据
    refreshData() {
        this.loadData()
    }

    // 加载数据
    async loadData() {
        if (!this.dateRange || this.dateRange.length !== 2) {
            return
        }

        this.loading = true
        try {
            await Promise.all([
                this.loadSummaryData(),
                this.loadRankData()
            ])
        } catch (error) {
            console.error('加载统计数据失败:', error)
            this.$message.error('加载统计数据失败')
        } finally {
            this.loading = false
        }
    }

    // 加载营业汇总数据
    async loadSummaryData() {
        const params = this.buildParams()
        try {
            const result = await apiWorkbenchSummary(params)
            this.summaryData = result
        } catch (error) {
            console.error('加载营业汇总失败:', error)
        }
    }

    // 加载业绩排名数据
    async loadRankData() {
        const params = this.buildParams()
        try {
            const result = await apiWorkbenchRank(params)
            this.rankData = result || []
        } catch (error) {
            console.error('加载业绩排名失败:', error)
        }
    }

    // 构建请求参数
    buildParams() {
        const params: any = {
            start_time: this.dateRange[0],
            end_time: this.dateRange[1]
        }

        // 根据管理员信息添加 sid 和 mid
        if (this.adminInfo) {
            if (this.adminInfo.sid) {
                params.sid = this.adminInfo.sid
            }
            if (this.adminInfo.mid) {
                params.mid = this.adminInfo.mid
            }
        }

        return params
    }

    // 获取排名样式类
    getRankClass(index: number): string {
        if (index === 0) {
return 'rank-first'
}
        if (index === 1) {
return 'rank-second'
}
        if (index === 2) {
return 'rank-third'
}
        return 'rank-normal'
    }
}
</script>

<style scoped lang="scss">
.business-statistics {
    .filter-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 16px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;

            .refresh-btn {
                margin-left: 8px;
            }
        }
    }

    .summary-section, .rank-section {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
    }

    .stat-card {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #409eff;

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
    }

    .rank-badge {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        color: #fff;

        &.rank-first {
            background: #f56c6c;
        }

        &.rank-second {
            background: #e6a23c;
        }

        &.rank-third {
            background: #67c23a;
        }

        &.rank-normal {
            background: #909399;
        }
    }

    .no-data {
        text-align: center;
        color: #999;
        padding: 40px;
        font-size: 14px;
    }
}
</style>
