import request from '@/plugins/axios'
import header from '@/components/decorate/pc-widgets/header'


// 子店铺列表

/*
{
  "code": 1,
  "show": 1,
  "msg": "成功",
  "data": [
    {
      "id": 2,
      "name": "测试一门店二"
    },
    {
      "id": 1,
      "name": "测试一门店一"
    }
  ]
}

*/
export const apiSubShopList = (params: any) => request.get('/branch.branch/lists', { params })


// 新增店铺
/*
  id：id
  address：店铺地址
  business_end_time：营业开始时间
  business_start_time：营业结束时间
  city：城市
  contact：联系人
  district：区（县）
  image：图片
  latitude：纬度
  mobile：联系电话
  name：门店名称
  province：省
  remark：备注
  status：状态
  weekdays：营业日
  images：（轮播图）图片集
  account：账号 店长登录账号（全局唯一）
  password：密码 店长登录密码（≥6 位）

*/

export const apiStoreSave = (params: any) => request.post('/branch.branch/add', params)


// 修改店铺

/*
  id：id
  address：店铺地址
  business_end_time：营业开始时间
  business_start_time：营业结束时间
  city：城市
  contact：联系人
  district：区（县）
  image：图片
  latitude：纬度
  mobile：联系电话
  name：门店名称
  province：省
  remark：备注
  status：状态
  weekdays：营业日
  images：（轮播图）图片集
  account：账号 店长登录账号（全局唯一）
  password：密码 店长登录密码（≥6 位）

*/
export const apiStoreUpdate = (params: any) => request.post('/branch.branch/edit', params)

// 店铺详情
/*
  id：店铺id
*/
export const apiStoreDetail = (params: any) => request.get('/branch.branch/detail', { params })

// 理发师列表


// 新增/修改理发师
/*
    id：不传id为新增
    name：理发师名称
    mobile：理发师手机号
    image：理发师头像
    shop_id：店铺id
    account：理发师账号
    password：理发师密码
*/
export const apiBarberSave = (params: any) => request.post('/barber/add', params)

// 删除理发师
/*
    id：理发师id
*/
export const apiBarberDel = (params: any) => request.get('/barber/del', { params })

// 理发师列表
/*
    page_size：每页条数
    page_no：当前页
    count：总条数
*/
export const apiBarberList = (params: any) => request.get('/barber/lists', { params })

// 理发师详情
/*
    id：理发师id
*/
export const apiBarberDetail = (params: any) => request.get('/barber/detail', { params })


// 获取预约收费配置
/*
* {
    "code": 1,
    "msg": "获取成功",
    "data": {
        "reservation_fee_enabled": 1,
        "reservation_fee": 20.00,
        "reservation_fee_desc": "预约服务费",
        "reservation_refund_enabled": 1,
        "reservation_refund_rule": "提前2小时取消可退还预约费"
    }
}
*● reservation_fee_enabled: 是否启用预约收费 (0-否, 1-是)
● reservation_fee: 预约费用金额
● reservation_fee_desc: 预约费用说明
● reservation_refund_enabled: 是否允许退还预约费 (0-否, 1-是)
● reservation_refund_rule: 退费规则说明
* */
export const apiAppointmentConfig = (params: any) => request.get('/reservation_config/get_config', { params })

// 设置预约收费配置
/*
● reservation_fee_enabled: 必填，是否启用预约收费 (0-否, 1-是)
● reservation_fee: 必填，预约费用金额，不能小于0
● reservation_fee_desc: 可选，预约费用说明，最多100字符
● reservation_refund_enabled: 必填，是否允许退费 (0-否, 1-是)
● reservation_refund_rule: 可选，退费规则说明，最多200字符
* */

export const apiAppointmentConfigSave = (params: any) => request.post('/reservation_config/set_config', params)


//确认预约 ： 接口描述: 确认预约（适用于已支付预约费的预约）
/*
* URL参数:
● id: 预约ID
*
* 请求参数:
* {"remark": "预约确认备注"}
* */
export const apiAppointmentConfirm = (urlParams: any, data: any) => request.post(`/reservation/confirm/${urlParams.id}`, data)

// 取消预约 ： 接口描述: 取消预约
/*
* URL参数:
● id: 预约ID
* 请求参数：
* {
    "reason": "取消原因",
    "refund_fee": true
}
*
● reason: 必填，取消原因
● refund_fee: 可选，是否退还预约费，默认根据退费规则处理
* */

export const apiAppointmentCancel = (urlParams: any, data: any) => request.post(`/reservation/cancel/${urlParams.id}`, data)

// 完成预约 ：标记预约为已完成

/*
*URL参数:
● id: 预约ID
*
* 请求参数:
* {
    "service_note": "服务完成备注"
}
* */

export const apiAppointmentComplete = (urlParams: any, data: any) => request.post(`/reservation/complete/${urlParams.id}`, data)


// 确认预约费支付 ： 手动确认预约费支付（适用于线下支付等场景）
/*
* URL参数:
● id: 预约ID
*
* 请求参数:
* {
    "pay_way": 1,
    "pay_note": "现金支付"
}
* 参数说明:
● pay_way: 支付方式 (1-现金, 2-微信, 3-支付宝, 4-银行卡)
● pay_note: 支付备注
* */
export const apiAppointmentPayConfirm = (urlParams: any, data: any) => request.post(`/reservation/confirm_payment/${urlParams.id}`, data)

//退还预约费 ： 接口描述: 退还预约费
/*
*URL参数:
● id: 预约ID
*
* 请求参数:
* {
    "refund_reason": "客户要求取消",
    "refund_amount": 20.00
}
*
* 参数说明:
● refund_reason: 必填，退费原因
● refund_amount: 可选，退费金额，默认为全额退费
* */
export const apiAppointmentRefund = (urlParams: any, data: any) => request.post(`/reservation/refund_fee/${urlParams.id}`, data)

// 获取预约统计数据 ： 接口描述: 获取预约相关的统计数据
/*
* 请求参数:
● start_date: 统计开始日期 (YYYY-MM-DD)
● end_date: 统计结束日期 (YYYY-MM-DD)
● barber_id: 可选，指定理发师ID
*
* 响应示例:
* {
    "code": 1,
    "msg": "获取成功",
    "data": {
        "total_reservations": 150,
        "confirmed_reservations": 120,
        "cancelled_reservations": 20,
        "completed_reservations": 100,
        "pending_payment": 10,
        "total_reservation_fee": 3000.00,
        "paid_reservation_fee": 2400.00,
        "refunded_reservation_fee": 400.00,
        "daily_statistics": [
            {
                "date": "2024-12-15",
                "total": 10,
                "confirmed": 8,
                "cancelled": 1,
                "completed": 7,
                "reservation_fee": 200.00
            },
            {
                "date": "2024-12-16",
                "total": 12,
                "confirmed": 10,
                "cancelled": 0,
                "completed": 8,
                "reservation_fee": 240.00
            }
        ]
    }
}
* */
export const apiAppointmentStats = (params: any) => request.get('/reservation/statistics', { params })


// 理发师预约列表
/*
    page_size：每页条数
    page_no：当前页
    id：理发师id
*/
export const apiBarberAppointmentList = (params: any) => request.get('/barber/reservation_list', { params })


// 项目列表
/*
  返回示例：{"code":1,"show":1,"msg":"成功","data":[{"id":4,"title":"项目三"},{"id":3,"title":"项目二"},{"id":2,"title":"项目一"}]}
*/
export const apiProjectList = (params: any) => request.get('/service_item/lists', { params })

// 新增/修改理发师项目
/*
  services_id：项目服务id
  price：项目价格
  barber_id：理发师id
*/
export const apiBarberProjectSave = (params: any) => request.get('/barber/services_add', { params })

// 删除理发师项目

/*
  id: 项目id
*/
export const apiBarberProjectDel = (params: any) => request.get('/barber/services_del', { params })

// 理发师项目列表
/*

  {
  "code": 1,
  "show": 1,
  "msg": "成功",
  "data": [
    {
      "id": 2,
      "price": "20",
      "title": "吹头发"
    },
    {
      "id": 3,
      "price": "50",
      "title": "剪头发"
    }
  ]
}
*/
export const apiBarberProjectList = (params: any) => request.get('/barber/services_lists', { params })

// 理发师项目详情
export const apiBarberProjectDetail = (params: any) => request.get('/barber/services_detail', { params })


// 新增/修改理发师作品

/*
  barber_id：理发师id
  title：作品标题
  images：作品图集
  content：作品文字描述
  barber_service_id：理发师项目id

*/
export const apiBarberWorksSave = (params: any) => request.get('/barber/works_add', { params })

// 删除理发师作品
/*
  id：作品id
*/
export const apiBarberWorksDel = (params: any) => request.get('/barber/works_del', { params })

// 理发师作品列表
/*

{
  "code": 1,
  "show": 1,
  "msg": "成功",
  "data": {
    "count": 1,
    "page_no": "1",
    "page_size": "10",
    "lists": [
      {
        "id": 3,
        "barber_id": 8,
        "shop_id": 2,
        "shop_sn": "cv9nf260",
        "title": "韩式空气刘海1",
        "image": "https://ceshiyi.yusengkeji.cn/uploads/cv9nf260/admin/images/20250416/20250416142046b053d2385.jpeg",
        "images": "https://ceshiyi.yusengkeji.cn/uploads/cv9nf260/admin/images/20250416/20250416142046b053d2385.jpeg,https://ceshiyi.yusengkeji.cn/uploads/cv9nf260/admin/images/20250416/20250416142046b053d2385.jpeg,https://ceshiyi.yusengkeji.cn/uploads/cv9nf260/admin/images/20250416/20250416142046b053d2385.jpeg",
        "content": "阿斯u好地方i啊是你的戛洒面积大阿斯没见到你面积是的呢飞机失地农民房间卡那仨寄快递那圣诞节卡菲纳打赏金发的发",
        "create_time": 1744945897,
        "delete_time": null,
        "barber_service_id": 6
      }
    ]
  }
}
*/

export const apiBarberWorksList = (params: any) => request.get('/barber/works_lists', { params })

// 理发师作品详情
/*
  id：作品id
*/
export const apiBarberWorksDetail = (params: any) => request.get('/barber/works_detail', { params })

// 理发师作品评论列表
/*
  id：作品id
*/
export const apiBarberWorksCommentList = (params: any) => request.get('/barber/comment_list', { params })

// 删除理发师作品评论
/*
  id：评论id
*/
export const apiBarberWorksCommentDel = (params: any) => request.post('/barber/comment_delete', params)


// 新增/修改项目
/*
  id：项目id
  title：项目名称
  desc：项目描述
  price：项目价格
  image：项目图片
  vip_price：会员价格
*/
export const apiProjectSave = (params: any) => request.get('/service_item/add', { params })

// 删除项目
/*
  id：项目id
*/
export const apiProjectDel = (params: any) => request.get('/service_item/del', { params })

// 项目列表
/*
  page_size：每页条数
  page_no：当前页
  count：总条数

  示例：{
  "code": 1,
  "show": 1,
  "msg": "成功",
  "data": [
    {
      "id": 4,
      "title": "项目三"
    },
    {
      "id": 3,
      "title": "项目二"
    },
    {
      "id": 2,
      "title": "项目一"
    }
  ]
}
*/
export const apiCreateProjectList = (params: any) => request.get('/service_item/lists', { params })

// 项目详情
/*
  id：项目id
*/
export const apiProjectDetail = (params: any) => request.get('/service_item/detail', { params })


// 理发师职位列表
/*
  page_size：每页条数
  page_no：当前页
*/
export const apiBarberJobList = (params: any) => request.get('/barber/job_lists', { params })
// 理发师职位详情
/*
  id：id
*/
export const apiBarberJobDetail = (params: any) => request.get('/barber/job_detail', { params })

// 新增/修改理发师职位
/*
  id：id
*/
export const apiBarberJobSave = (params: any) => request.get('/barber/job_add', { params })

// 删除理发师职位
/*
  id：id
  shop_id：职位名称
  commission:分成比例
*/
export const apiBarberJobDel = (params: any) => request.post('/barber/job_delete', params)


