<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { apiUserCardList, apiMemberList, apiMemberUsageRecord } from '@/api/member/member'
import config from '@/config'

@Component({
    name: 'MemberCardDetail'
})
export default class MemberCardDetail extends Vue {
    // 会员ID
    private memberId = ''

    // 会员信息
    private memberInfo: any = {}

    // 会员卡列表
    private cardList: any[] = []

    // 使用记录列表
    private usageRecords: any[] = []

    // 充值/开卡记录列表 (type = 1)
    private rechargeRecords: any[] = []

    // 消费/使用记录列表 (type = 2)
    private consumeRecords: any[] = []

    // 加载状态
    private loading = false

    // 配置信息
    private baseURL = config.baseURL

    created() {
        // 获取路由参数中的会员ID
        const id = this.$route.query.id
        if (id) {
            this.memberId = id as string
            this.getMemberInfo()
            this.getCardList()
            this.getUsageRecord()
        } else {
            this.$message.error('会员ID不能为空')
            this.goBack()
        }
    }

    // 获取会员信息
    async getMemberInfo() {
        try {
            this.loading = true
            const res = await apiMemberList({ id: this.memberId })
            if (res && res.lists && res.lists.length > 0) {
                console.log('会员信息', res.lists[0])
                this.memberInfo = res.lists[0]
            } else {
                this.$message.error('会员不存在')
                this.goBack()
            }
        } catch (error) {
            console.error('获取会员信息失败', error)
            this.$message.error('获取会员信息失败')
        } finally {
            this.loading = false
        }
    }

    // 获取会员卡列表
    async getCardList() {
        try {
            this.loading = true
            const res = await apiUserCardList({ id: this.memberId })
            if (res && res.lists) {
                this.cardList = res.lists
            }
        } catch (error) {
            console.error('获取会员卡列表失败', error)
            this.$message.error('获取会员卡列表失败')
        } finally {
            this.loading = false
        }
    }

    // 获取使用记录
    async getUsageRecord() {
        try {
            this.loading = true
            const res = await apiMemberUsageRecord({ id: this.memberId })
            if (res && res.lists) {
                this.usageRecords = res.lists
                // 根据 type 字段分类数据
                this.rechargeRecords = res.lists.filter((item: any) => item.type === 1) // 充值/开卡记录
                this.consumeRecords = res.lists.filter((item: any) => item.type === 2) // 消费/使用记录
            }
        } catch (error) {
            console.error('获取使用记录失败', error)
            this.$message.error('获取使用记录失败')
        } finally {
            this.loading = false
        }
    }

    // 返回会员列表
    goBack() {
        this.$router.push('/member/list')
    }

    // 格式化时间
    formatTime(timestamp: number) {
        if (!timestamp) {
            return '-'
        }
        const date = new Date(timestamp * 1000)
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
            2,
            '0'
        )}`
    }

    // 获取卡片背景色
    getCardBackground(type: number, index: number) {
        // 会员卡和次卡使用不同的颜色系列
        const memberCardColors = [
            'linear-gradient(135deg, #8E2DE2 0%, #4A00E0 100%)',
            'linear-gradient(135deg, #FF8008 0%, #FFC837 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        ]

        const countCardColors = [
            'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
            'linear-gradient(135deg, #F6D365 0%, #FDA085 100%)',
            'linear-gradient(135deg, #6A11CB 0%, #2575FC 100%)'
        ]

        const colors = type === 1 ? memberCardColors : countCardColors
        return colors[index % colors.length]
    }

    // 获取进度百分比
    getProgressPercentage(used: any, total: any) {
        // 确保转换为数字类型
        const usedNum = parseFloat(used) || 0
        const totalNum = parseFloat(total) || 1 // 避免除以0

        // 计算百分比并确保在0-100之间
        let percentage = (usedNum / totalNum) * 100

        // 限制在0-100范围内
        percentage = Math.max(0, Math.min(100, percentage))

        // 确保是有效数字
        return isNaN(percentage) ? 0 : percentage
    }

    // 根据卡ID获取卡名称
    getCardNameById(cardId: number) {
        const card = this.cardList.find(item => item.id === cardId)
        if (card) {
            return card.type === 1 ? '会员储值卡' : card.title
        }
        return '未知卡'
    }
}
</script>

<template>
    <div class="member-card-detail-container" v-loading="loading">
        <!-- 导航头部 -->
        <div class="header-container">
            <el-page-header @back="goBack" content="会员卡详情" />
        </div>

        <!-- 会员信息 -->
        <div class="member-info-container" v-if="memberInfo.id">
            <div class="member-avatar">
                <el-avatar :size="80" :src="baseURL + '/' + memberInfo.avatar"></el-avatar>
            </div>
            <div class="member-info">
                <h2 class="member-name">{{ memberInfo.nickname }}</h2>
                <p class="member-mobile">手机号: {{ memberInfo.mobile }}</p>
                <p class="member-points">积分: {{ memberInfo.points }}</p>
                <p class="member-sn">会员编号: {{ memberInfo.sn }}</p>
            </div>
        </div>

        <!-- 会员卡列表 -->
        <div class="card-list-container">
            <h3 class="section-title">会员卡列表</h3>

            <el-empty description="暂无会员卡" v-if="cardList.length === 0"></el-empty>

            <div class="card-list" v-else>
                <!-- 会员卡 -->
                <div
                    v-for="(card, index) in cardList"
                    :key="card.id"
                    class="card-item"
                    :style="{ background: getCardBackground(card.type, index) }"
                >
                    <div class="card-header">
                        <div class="card-title">
                            {{ card.type === 1 ? '会员储值卡' : card.title }}
                        </div>
                        <div class="card-type-tag">
                            {{ card.type === 1 ? '储值卡' : '次卡' }}
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- 会员卡余额 -->
                        <template v-if="card.type === 1">
                            <div class="card-amount">
                                <span class="amount-label">余额</span>
                                <span class="amount-value">¥{{ card.remain_price }}</span>
                            </div>
                            <div class="card-progress">
                                <div class="progress-info">
                                    <span>已用: ¥{{ card.used_price }}</span>
                                    <span>总额: ¥{{ card.price }}</span>
                                </div>
                                <el-progress
                                    :percentage="getProgressPercentage(card.used_price, card.price)"
                                    :show-text="false"
                                    :stroke-width="8"
                                    color="#ffffff"
                                    :track-color="'rgba(255, 255, 255, 0.2)'"
                                ></el-progress>
                            </div>
                        </template>

                        <!-- 次卡信息 -->
                        <template v-else>
                            <div class="card-amount">
                                <span class="amount-label">剩余次数</span>
                                <span class="amount-value">{{ card.remain_num }}次</span>
                            </div>
                            <div class="card-progress">
                                <div class="progress-info">
                                    <span>已用: {{ card.used_num }}次</span>
                                    <span>总次数: {{ card.num }}次</span>
                                </div>
                                <el-progress
                                    :percentage="getProgressPercentage(card.used_num, card.num)"
                                    :show-text="false"
                                    :stroke-width="8"
                                    color="#ffffff"
                                    :track-color="'rgba(255, 255, 255, 0.2)'"
                                ></el-progress>
                            </div>
                        </template>
                    </div>

                    <div class="card-footer">
                        <div class="card-date">开卡日期: {{ formatTime(card.create_time) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值/开卡记录 -->
        <div class="usage-records-container">
            <h3 class="section-title">充值/开卡记录</h3>

            <el-empty description="暂无充值/开卡记录" v-if="rechargeRecords.length === 0"></el-empty>

            <el-table
                v-else
                :data="rechargeRecords"
                style="width: 100%"
                border
                stripe
            >
                <el-table-column
                    prop="service_title"
                    label="服务名称"
                    min-width="150"
                >
                </el-table-column>
                <el-table-column
                    prop="price"
                    label="金额"
                    min-width="100"
                >
                    <template slot-scope="scope">
                        ¥{{ scope.row.price }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="card_id"
                    label="充值卡"
                    min-width="150"
                >
                    <template slot-scope="scope">
                        {{ getCardNameById(scope.row.card_id) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="描述"
                    min-width="200"
                >
                    <template slot-scope="scope">
                        {{ scope.row.remark || '-' }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="充值时间"
                    min-width="150"
                >
                    <template slot-scope="scope">
                        {{ scope.row.create_time }}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 消费/使用记录 -->
        <div class="usage-records-container">
            <h3 class="section-title">消费/使用记录</h3>

            <el-empty description="暂无消费/使用记录" v-if="consumeRecords.length === 0"></el-empty>

            <el-table
                v-else
                :data="consumeRecords"
                style="width: 100%"
                border
                stripe
            >
                <el-table-column
                    prop="service_title"
                    label="服务名称"
                    min-width="150"
                >
                </el-table-column>
                <el-table-column
                    prop="price"
                    label="消费金额"
                    min-width="100"
                >
                    <template slot-scope="scope">
                        ¥{{ scope.row.price }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="card_id"
                    label="使用卡"
                    min-width="150"
                >
                    <template slot-scope="scope">
                        {{ getCardNameById(scope.row.card_id) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="描述"
                    min-width="200"
                >
                    <template slot-scope="scope">
                        {{ scope.row.remark || '-' }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="消费时间"
                    min-width="150"
                >
                    <template slot-scope="scope">
                        {{ scope.row.create_time }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.member-card-detail-container {
    padding: 20px;

    .header-container {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }

    .member-info-container {
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

        .member-avatar {
            margin-right: 20px;

            .el-avatar {
                border: 2px solid #fff;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }
        }

        .member-info {
            .member-name {
                margin: 0 0 10px 0;
                font-size: 20px;
            }

            .member-mobile,
            .member-points,
            .member-sn {
                margin: 5px 0;
                color: #666;
            }
        }
    }

    .section-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    .card-list-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

        .card-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;

            .card-item {
                width: calc(33.33% - 20px);
                min-width: 300px;
                height: 180px;
                border-radius: 12px;
                padding: 20px;
                color: #fff;
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
                display: flex;
                flex-direction: column;
                position: relative;
                overflow: hidden;
                transition: transform 0.3s ease;

                &:hover {
                    transform: translateY(-5px);
                }

                &::before {
                    content: '';
                    position: absolute;
                    top: -80%;
                    right: -80%;
                    width: 150%;
                    height: 150%;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.1);
                    pointer-events: none;
                }

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;

                    .card-title {
                        font-size: 20px;
                        font-weight: bold;
                    }

                    .card-type-tag {
                        background-color: rgba(255, 255, 255, 0.2);
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                    }
                }

                .card-body {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .card-amount {
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 15px;

                        .amount-label {
                            font-size: 14px;
                            opacity: 0.8;
                            margin-bottom: 5px;
                        }

                        .amount-value {
                            font-size: 28px;
                            font-weight: bold;
                        }
                    }

                    .card-progress {
                        .progress-info {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 5px;
                            font-size: 12px;
                            opacity: 0.8;
                        }
                    }
                }

                .card-footer {
                    margin-top: 15px;
                    font-size: 12px;
                    opacity: 0.8;
                }
            }
        }
    }

    .usage-records-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
}

@media (max-width: 768px) {
    .card-list-container .card-list .card-item {
        width: 100%;
    }
}
</style>
