import request from '@/plugins/axios'

// 商品列表
export const apiWorkbenchIndex = (data: any) => request.get('/workbench/index', data)

// 商品top50
export const apiWorkbenchTopGoods = (params: any) =>
    request.get('/workbench/topGoods50', { params })

// 用户top50
export const apiWorkbenchTopUser = (params: any) => request.get('/workbench/topUser50', { params })

// 联系多开版平台客服
export const apiServiceContact = () => request.get('/workbench/getPlatformService')

// 检测更新
export const apiCheckRefresh = (params?: any) => request.get('/config/checkLegal', { params })


// 营业汇总
/*
* sid:店铺id
* mid:门店id
* start_time:开始时间
* end_time:结束时间
*
* */
export const apiWorkbenchSummary = (params: any) => request.get('/cashier/summary', { params })

//业绩排名
/*
* sid:店铺id
* mid:门店id
* start_time:开始时间
* end_time:结束时间
*
* */
export const apiWorkbenchRank = (params: any) => request.get('/cashier/performance', { params })
