<template>
    <div class="style-list flex row-center">
        <img v-if="content.style == 1" src="@/assets/images/sort_one.png" />
        <img v-if="content.style == 2" src="@/assets/images/sort_one2.png" />
        <img v-if="content.style == 3" src="@/assets/images/sort_one3.png" />
        <img v-if="content.style == 4" src="@/assets/images/sort_one4.png" />
        <img v-if="content.style == 5" src="@/assets/images/sort_two.png" />
        <img v-if="content.style == 6" src="@/assets/images/sort_two2.png" />
        <img v-if="content.style == 7" src="@/assets/images/sort_three.png" />
        <img v-if="content.style == 8" src="@/assets/images/sort_three2.png" />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
@Component
export default class StyleList extends Vue {
    @Prop() content!: any
}
</script>

<style lang="scss" scoped>
.style-list {
    height: 100%;
    img {
        height: 100%;
    }
}
</style>
