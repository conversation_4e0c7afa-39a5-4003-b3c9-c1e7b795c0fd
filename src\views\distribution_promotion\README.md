# 分销推广系统

基于接口文档实现的全新分销用户管理和提现管理系统。

## 功能模块

### 1. 分销统计概览 (`/distribution_promotion/statistics`)
- 总体数据统计（分销用户数、活跃用户数、总邀请数、总佣金）
- 今日数据统计（新增用户、新增邀请、今日佣金）
- 本月数据统计（新增用户、新增邀请、本月佣金）
- 提现数据统计（总提现、待审核、成功提现）
- 等级分布统计表格
- 提现统计详情（包含提现方式统计）

### 2. 分销用户管理 (`/distribution_promotion/user_list`)
- 用户列表展示（昵称、手机号、等级、邀请人数、总佣金、可用余额等）
- 搜索筛选功能（昵称、手机号、等级、状态）
- 用户状态管理（启用/禁用）
- 分页功能
- 查看用户详情

### 3. 分销用户详情 (`/distribution_promotion/user_detail`)
- 用户基本信息展示
- 佣金统计（总佣金、已结算、待结算）
- 提现统计（总提现、成功提现、待审核）
- 一级下级用户列表
- 二级下级用户列表

### 4. 提现管理 (`/distribution_promotion/withdraw_list`)
- 提现申请列表展示
- 搜索筛选功能（用户昵称、手机号、状态）
- 提现审核功能（通过/拒绝）
- 审核备注功能
- 分页功能

## API接口

### 分销用户管理接口
- `GET /distribution_promotion/user_list` - 分销用户列表
- `GET /distribution_promotion/user_detail` - 分销用户详情
- `GET /distribution_promotion/statistics` - 分销统计概览
- `GET /distribution_promotion/level_statistics` - 等级分布统计
- `POST /distribution_promotion/change_status` - 禁用/启用分销用户

### 提现管理接口
- `GET /distribution_withdraw/list` - 提现申请列表
- `POST /distribution_withdraw/audit` - 审核提现申请
- `GET /distribution_withdraw/statistics` - 提现统计

## 文件结构

```
src/
├── api/distribution_promotion/
│   ├── distribution_promotion.ts      # API接口定义
│   └── distribution_promotion.d.ts    # TypeScript类型定义
├── views/distribution_promotion/
│   ├── statistics.vue                 # 分销统计概览页面
│   ├── user_list.vue                  # 分销用户列表页面
│   ├── user_detail.vue                # 分销用户详情页面
│   └── withdraw_list.vue              # 提现管理页面
└── router/modules/
    └── application.ts                 # 路由配置（已更新）
```

## 使用说明

1. **访问分销统计**：导航到 `/distribution_promotion/statistics` 查看整体数据概览
2. **管理分销用户**：导航到 `/distribution_promotion/user_list` 查看和管理分销用户
3. **查看用户详情**：在用户列表中点击"查看详情"按钮
4. **管理提现申请**：导航到 `/distribution_promotion/withdraw_list` 处理提现申请

## 技术特点

- 使用 Vue.js + TypeScript + Element UI
- 完整的类型定义支持
- 响应式设计，支持移动端
- 统一的错误处理和加载状态
- 符合现有项目的代码规范和架构模式

## 注意事项

1. 所有API接口都需要后端实现对应的接口
2. 权限控制需要在后端实现
3. 数据验证和安全性检查需要在后端处理
4. 建议在生产环境中添加更多的错误处理和用户体验优化
