/* Logo 字体 */
@font-face {
    font-family: 'iconfont logo';
    src: url('https://at.alicdn.com/t/font_985780_km7mi63cihi.eot?t=1545807318834');
    src: url('https://at.alicdn.com/t/font_985780_km7mi63cihi.eot?t=1545807318834#iefix')
            format('embedded-opentype'),
        url('https://at.alicdn.com/t/font_985780_km7mi63cihi.woff?t=1545807318834') format('woff'),
        url('https://at.alicdn.com/t/font_985780_km7mi63cihi.ttf?t=1545807318834')
            format('truetype'),
        url('https://at.alicdn.com/t/font_985780_km7mi63cihi.svg?t=1545807318834#iconfont')
            format('svg');
}

.logo {
    font-family: 'iconfont logo';
    font-size: 160px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* tabs */
.nav-tabs {
    position: relative;
}

.nav-tabs .nav-more {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 42px;
    line-height: 42px;
    color: #666;
}

#tabs {
    border-bottom: 1px solid #eee;
}

#tabs li {
    cursor: pointer;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
    border-bottom: 2px solid transparent;
    position: relative;
    z-index: 1;
    margin-bottom: -1px;
    color: #666;
}

#tabs .active {
    border-bottom-color: #f00;
    color: #222;
}

.tab-container .content {
    display: none;
}

/* 页面布局 */
.main {
    padding: 30px 100px;
    width: 960px;
    margin: 0 auto;
}

.main .logo {
    color: #333;
    text-align: left;
    margin-bottom: 30px;
    line-height: 1;
    height: 110px;
    margin-top: -50px;
    overflow: hidden;
    *zoom: 1;
}

.main .logo a {
    font-size: 160px;
    color: #333;
}

.helps {
    margin-top: 40px;
}

.helps pre {
    padding: 20px;
    margin: 10px 0;
    border: solid 1px #e7e1cd;
    background-color: #fffdef;
    overflow: auto;
}

.icon_lists {
    width: 100% !important;
    overflow: hidden;
    *zoom: 1;
}

.icon_lists li {
    width: 100px;
    margin-bottom: 10px;
    margin-right: 20px;
    text-align: center;
    list-style: none !important;
    cursor: default;
}

.icon_lists li .code-name {
    line-height: 1.2;
}

.icon_lists .icon {
    display: block;
    height: 100px;
    line-height: 100px;
    font-size: 42px;
    margin: 10px auto;
    color: #333;
    -webkit-transition: font-size 0.25s linear, width 0.25s linear;
    -moz-transition: font-size 0.25s linear, width 0.25s linear;
    transition: font-size 0.25s linear, width 0.25s linear;
}

.icon_lists .icon:hover {
    font-size: 100px;
}

.icon_lists .svg-icon {
    /* 通过设置 font-size 来改变图标大小 */
    width: 1em;
    /* 图标和文字相邻时，垂直对齐 */
    vertical-align: -0.15em;
    /* 通过设置 color 来改变 SVG 的颜色/fill */
    fill: currentColor;
    /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
      normalize.css 中也包含这行 */
    overflow: hidden;
}

.icon_lists li .name,
.icon_lists li .code-name {
    color: #666;
}

/* markdown 样式 */
.markdown {
    color: #666;
    font-size: 14px;
    line-height: 1.8;
}

.highlight {
    line-height: 1.5;
}

.markdown img {
    vertical-align: middle;
    max-width: 100%;
}

.markdown h1 {
    color: #404040;
    font-weight: 500;
    line-height: 40px;
    margin-bottom: 24px;
}

.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
    color: #404040;
    margin: 1.6em 0 0.6em 0;
    font-weight: 500;
    clear: both;
}

.markdown h1 {
    font-size: 28px;
}

.markdown h2 {
    font-size: 22px;
}

.markdown h3 {
    font-size: 16px;
}

.markdown h4 {
    font-size: 14px;
}

.markdown h5 {
    font-size: 12px;
}

.markdown h6 {
    font-size: 12px;
}

.markdown hr {
    height: 1px;
    border: 0;
    background: #e9e9e9;
    margin: 16px 0;
    clear: both;
}

.markdown p {
    margin: 1em 0;
}

.markdown > p,
.markdown > blockquote,
.markdown > .highlight,
.markdown > ol,
.markdown > ul {
    width: 80%;
}

.markdown ul > li {
    list-style: circle;
}

.markdown > ul li,
.markdown blockquote ul > li {
    margin-left: 20px;
    padding-left: 4px;
}

.markdown > ul li p,
.markdown > ol li p {
    margin: 0.6em 0;
}

.markdown ol > li {
    list-style: decimal;
}

.markdown > ol li,
.markdown blockquote ol > li {
    margin-left: 20px;
    padding-left: 4px;
}

.markdown code {
    margin: 0 3px;
    padding: 0 5px;
    background: #eee;
    border-radius: 3px;
}

.markdown strong,
.markdown b {
    font-weight: 600;
}

.markdown > table {
    border-collapse: collapse;
    border-spacing: 0px;
    empty-cells: show;
    border: 1px solid #e9e9e9;
    width: 95%;
    margin-bottom: 24px;
}

.markdown > table th {
    white-space: nowrap;
    color: #333;
    font-weight: 600;
}

.markdown > table th,
.markdown > table td {
    border: 1px solid #e9e9e9;
    padding: 8px 16px;
    text-align: left;
}

.markdown > table th {
    background: #f7f7f7;
}

.markdown blockquote {
    font-size: 90%;
    color: #999;
    border-left: 4px solid #e9e9e9;
    padding-left: 0.8em;
    margin: 1em 0;
}

.markdown blockquote p {
    margin: 0;
}

.markdown .anchor {
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-left: 8px;
}

.markdown .waiting {
    color: #ccc;
}

.markdown h1:hover .anchor,
.markdown h2:hover .anchor,
.markdown h3:hover .anchor,
.markdown h4:hover .anchor,
.markdown h5:hover .anchor,
.markdown h6:hover .anchor {
    opacity: 1;
    display: inline-block;
}

.markdown > br,
.markdown > p > br {
    clear: both;
}

.hljs {
    display: block;
    background: white;
    padding: 0.5em;
    color: #333333;
    overflow-x: auto;
}

.hljs-comment,
.hljs-meta {
    color: #969896;
}

.hljs-string,
.hljs-variable,
.hljs-template-variable,
.hljs-strong,
.hljs-emphasis,
.hljs-quote {
    color: #df5000;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-type {
    color: #a71d5d;
}

.hljs-literal,
.hljs-symbol,
.hljs-bullet,
.hljs-attribute {
    color: #0086b3;
}

.hljs-section,
.hljs-name {
    color: #63a35c;
}

.hljs-tag {
    color: #333333;
}

.hljs-title,
.hljs-attr,
.hljs-selector-id,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo {
    color: #795da3;
}

.hljs-addition {
    color: #55a532;
    background-color: #eaffea;
}

.hljs-deletion {
    color: #bd2c00;
    background-color: #ffecec;
}

.hljs-link {
    text-decoration: underline;
}

/* 代码高亮 */
/* PrismJS 1.15.0
https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript */
/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */
code[class*='language-'],
pre[class*='language-'] {
    color: black;
    background: none;
    text-shadow: 0 1px white;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;

    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;

    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
}

pre[class*='language-']::-moz-selection,
pre[class*='language-'] ::-moz-selection,
code[class*='language-']::-moz-selection,
code[class*='language-'] ::-moz-selection {
    text-shadow: none;
    background: #b3d4fc;
}

pre[class*='language-']::selection,
pre[class*='language-'] ::selection,
code[class*='language-']::selection,
code[class*='language-'] ::selection {
    text-shadow: none;
    background: #b3d4fc;
}

@media print {
    code[class*='language-'],
    pre[class*='language-'] {
        text-shadow: none;
    }
}

/* Code blocks */
pre[class*='language-'] {
    padding: 1em;
    margin: 0.5em 0;
    overflow: auto;
}

:not(pre) > code[class*='language-'],
pre[class*='language-'] {
    background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*='language-'] {
    padding: 0.1em;
    border-radius: 0.3em;
    white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: slategray;
}

.token.punctuation {
    color: #999;
}

.namespace {
    opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
    color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #9a6e3a;
    background: hsla(0, 0%, 100%, 0.5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: #07a;
}

.token.function,
.token.class-name {
    color: #dd4a68;
}

.token.regex,
.token.important,
.token.variable {
    color: #e90;
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}
